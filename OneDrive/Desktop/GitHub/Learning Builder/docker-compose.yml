version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: learning_builder_db
      POSTGRES_USER: postgres-user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=True
      - DB_NAME=learning_builder_db
      - DB_USER=postgres-user
      - DB_PASSWORD=password
      - DB_HOST=db
      - DB_PORT=5432
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - db
      - redis
    volumes:
      - .:/app
      - media_files:/app/media

  celery:
    build: .
    command: celery -A Learning_Builder worker --loglevel=info
    environment:
      - DEBUG=True
      - DB_NAME=learning_builder_db
      - DB_USER=postgres-user
      - DB_PASSWORD=password
      - DB_HOST=db
      - DB_PORT=5432
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - db
      - redis
    volumes:
      - .:/app
      - media_files:/app/media

  frontend:
    build:
      context: ./ai-tutor-frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
    volumes:
      - ./ai-tutor-frontend:/app
      - /app/node_modules

volumes:
  postgres_data:
  media_files:
