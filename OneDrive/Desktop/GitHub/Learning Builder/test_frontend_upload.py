#!/usr/bin/env python
"""
Тестов скрипт за симулиране на frontend upload заявка.
"""
import requests
import os

def test_frontend_upload():
    """Симулира точно същата заявка която прави frontend."""
    print("🧪 Симулиране на frontend upload заявка...")
    
    # Създаване на тестов файл
    test_content = """This is a comprehensive test document for frontend testing. 
Python is a high-level programming language known for its simplicity and readability.
Django is a powerful web framework for Python that follows the model-view-template (MVT) pattern.
It includes many built-in features like an ORM, admin interface, URL routing, and template engine.
Python supports multiple programming paradigms including object-oriented, functional, and procedural programming.
Django encourages rapid development and clean, pragmatic design principles.
The framework follows the DRY (Don't Repeat Yourself) principle to reduce code duplication.
Python's extensive standard library and third-party packages make it suitable for various applications.
Django provides built-in security features to protect against common web vulnerabilities.
This document contains sufficient content to test the AI generation capabilities."""
    
    test_file_path = "test_frontend.txt"
    
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    try:
        # Симулиране на frontend заявка към Vite proxy
        print("📤 1. Тест към Vite proxy (localhost:5174)...")
        with open(test_file_path, 'rb') as f:
            files = {'file': ('test_frontend.txt', f, 'text/plain')}
            data = {'title': 'Frontend Test Document'}
            
            # Headers които изпраща браузъра
            headers = {
                'Accept': '*/*',
                'Origin': 'http://localhost:5174',
                'Referer': 'http://localhost:5174/',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0'
            }
            
            response = requests.post(
                "http://localhost:5174/api/documents/upload/", 
                files=files, 
                data=data,
                headers=headers
            )
            
            print(f"📊 Статус код: {response.status_code}")
            print(f"📄 Отговор: {response.text}")
            
            if response.status_code == 201:
                print("✅ Frontend upload успешен!")
                return response.json()
            else:
                print("❌ Frontend upload неуспешен!")
                
                # Опитай директно към Django
                print("\n📤 2. Тест директно към Django (localhost:8000)...")
                with open(test_file_path, 'rb') as f2:
                    files2 = {'file': ('test_frontend.txt', f2, 'text/plain')}
                    data2 = {'title': 'Frontend Test Document Direct'}
                    
                    response2 = requests.post(
                        "http://localhost:8000/api/documents/upload/", 
                        files=files2, 
                        data=data2
                    )
                    
                    print(f"📊 Django статус код: {response2.status_code}")
                    print(f"📄 Django отговор: {response2.text}")
                    
                    if response2.status_code == 201:
                        print("✅ Django upload работи!")
                        print("❌ Проблемът е в Vite proxy конфигурацията")
                    else:
                        print("❌ Проблемът е в Django backend")
                
                return None
                
    except Exception as e:
        print(f"❌ Грешка: {e}")
        return None
    finally:
        # Cleanup
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

def test_vite_proxy():
    """Тества дали Vite proxy работи правилно."""
    print("\n🔍 Тестване на Vite proxy...")
    
    try:
        # Тест 1: GET заявка
        print("📤 GET заявка към /api/documents/...")
        response = requests.get("http://localhost:5174/api/documents/")
        print(f"📊 GET статус: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ GET proxy работи")
        else:
            print("❌ GET proxy не работи")
        
        # Тест 2: OPTIONS заявка (CORS preflight)
        print("📤 OPTIONS заявка за CORS...")
        headers = {
            'Origin': 'http://localhost:5174',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        
        response = requests.options("http://localhost:5174/api/documents/upload/", headers=headers)
        print(f"📊 OPTIONS статус: {response.status_code}")
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
        }
        
        print("🔍 CORS Headers:")
        for header, value in cors_headers.items():
            print(f"   {header}: {value}")
        
        if response.status_code == 200 and cors_headers['Access-Control-Allow-Origin']:
            print("✅ CORS proxy работи")
        else:
            print("❌ CORS proxy не работи")
            
    except Exception as e:
        print(f"❌ Proxy тест грешка: {e}")

if __name__ == "__main__":
    print("🔍 Frontend Upload Testing")
    print("=" * 50)
    
    # Test Vite proxy
    test_vite_proxy()
    
    # Test upload
    result = test_frontend_upload()
    
    if result:
        print(f"\n🎉 Frontend upload работи!")
        print(f"📋 Документ ID: {result.get('id')}")
    else:
        print(f"\n❌ Frontend upload има проблеми.")
    
    print("\n🏁 Тестването завърши!")
