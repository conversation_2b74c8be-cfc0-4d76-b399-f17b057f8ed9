import requests
import json

# Get a document ID first
docs_response = requests.get('http://localhost:8000/api/documents/')
if docs_response.status_code == 200:
    docs = docs_response.json()
    if docs:
        doc_id = docs[0]['id']
        
        # Get study sets for this document
        sets_response = requests.get(f'http://localhost:8000/api/study-sets/?document={doc_id}')
        if sets_response.status_code == 200:
            sets = sets_response.json()
            quiz_sets = [s for s in sets if s['content_type'] == 'quiz']
            if quiz_sets:
                quiz_set_id = quiz_sets[0]['id']
                
                # Get quiz from study set
                quiz_response = requests.get(f'http://localhost:8000/api/study-sets/{quiz_set_id}/quiz/')
                if quiz_response.status_code == 200:
                    quiz = quiz_response.json()
                    
                    if quiz.get('questions'):
                        print('All questions analysis:')
                        for i, q in enumerate(quiz['questions'][:3]):  # First 3 questions
                            print(f'Question {i+1}:')
                            print(f'  Text: {q.get("question_text", "N/A")}')
                            print(f'  Choices: {q.get("choices", [])}')
                            print(f'  Correct choice: {repr(q.get("correct_choice", "N/A"))}')
                            
                            # Check if correct_choice is a letter or full text
                            correct = q.get('correct_choice', '')
                            choices = q.get('choices', [])
                            if correct in ['A', 'B', 'C', 'D']:
                                print(f'  -> Letter format detected: {correct}')
                                if correct == 'A' and len(choices) > 0:
                                    print(f'  -> Should be: {choices[0]}')
                                elif correct == 'B' and len(choices) > 1:
                                    print(f'  -> Should be: {choices[1]}')
                                elif correct == 'C' and len(choices) > 2:
                                    print(f'  -> Should be: {choices[2]}')
                                elif correct == 'D' and len(choices) > 3:
                                    print(f'  -> Should be: {choices[3]}')
                            else:
                                print(f'  -> Full text format: {correct}')
