#!/usr/bin/env python
"""
Тестов скрипт за frontend API endpoints.
"""
import requests
import os

BASE_URL = "http://127.0.0.1:8000"

def test_upload_and_generate():
    """Тества пълния workflow: upload -> generate flashcards -> generate quiz."""
    print("🧪 Тестване на пълния workflow...")
    
    # 1. Upload документ
    test_content = """This is a comprehensive test document for API testing. 
Python is a high-level programming language known for its simplicity and readability.
Django is a powerful web framework for Python that follows the model-view-template (MVT) pattern.
It includes many built-in features like an ORM, admin interface, URL routing, and template engine.
Python supports multiple programming paradigms including object-oriented, functional, and procedural programming.
Django encourages rapid development and clean, pragmatic design principles.
The framework follows the DRY (Don't Repeat Yourself) principle to reduce code duplication.
Python's extensive standard library and third-party packages make it suitable for various applications.
Django provides built-in security features to protect against common web vulnerabilities.
This document contains sufficient content to test the AI generation capabilities."""
    
    test_file_path = "test_comprehensive.txt"
    
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    try:
        # Upload
        print("📤 1. Качване на документ...")
        with open(test_file_path, 'rb') as f:
            files = {'file': ('test_comprehensive.txt', f, 'text/plain')}
            data = {'title': 'Comprehensive Test Document'}
            
            response = requests.post(f"{BASE_URL}/api/documents/upload/", files=files, data=data)
            
            if response.status_code != 201:
                print(f"❌ Upload неуспешен: {response.status_code} - {response.text}")
                return None
            
            doc_data = response.json()
            doc_id = doc_data['id']
            print(f"✅ Документ качен успешно: {doc_id}")
        
        # 2. Generate flashcards
        print("🃏 2. Генериране на флашкарти...")
        response = requests.post(f"{BASE_URL}/api/documents/{doc_id}/generate/flashcards/")
        
        if response.status_code != 200:
            print(f"❌ Генериране на флашкарти неуспешно: {response.status_code} - {response.text}")
        else:
            flashcard_data = response.json()
            print(f"✅ Флашкарти генерирани: {flashcard_data.get('flashcards_count', 0)} броя")
            flashcard_study_set_id = flashcard_data.get('study_set_id')
        
        # 3. Generate quiz
        print("📝 3. Генериране на тест...")
        response = requests.post(f"{BASE_URL}/api/documents/{doc_id}/generate/quiz/")
        
        if response.status_code != 200:
            print(f"❌ Генериране на тест неуспешно: {response.status_code} - {response.text}")
        else:
            quiz_data = response.json()
            print(f"✅ Тест генериран: {quiz_data.get('questions_count', 0)} въпроса")
            quiz_study_set_id = quiz_data.get('study_set_id')
            
            # 4. Test quiz endpoint
            print("🎯 4. Тестване на quiz endpoint...")
            response = requests.get(f"{BASE_URL}/api/study-sets/{quiz_study_set_id}/quiz/")
            
            if response.status_code != 200:
                print(f"❌ Quiz endpoint неуспешен: {response.status_code} - {response.text}")
            else:
                quiz_details = response.json()
                print(f"✅ Quiz endpoint работи: {quiz_details.get('title', 'N/A')}")
                print(f"   Въпроси: {len(quiz_details.get('questions', []))}")
        
        # 5. Test study sets list
        print("📚 5. Тестване на study sets list...")
        response = requests.get(f"{BASE_URL}/api/study-sets/?document={doc_id}")
        
        if response.status_code != 200:
            print(f"❌ Study sets list неуспешен: {response.status_code} - {response.text}")
        else:
            study_sets = response.json()
            print(f"✅ Study sets list работи: {len(study_sets)} study sets")
            for study_set in study_sets:
                print(f"   - {study_set['name']} ({study_set['content_type']})")
        
        # 6. Test flashcards endpoint
        if 'flashcard_study_set_id' in locals():
            print("🃏 6. Тестване на flashcards endpoint...")
            response = requests.get(f"{BASE_URL}/api/study-sets/{flashcard_study_set_id}/flashcards/")
            
            if response.status_code != 200:
                print(f"❌ Flashcards endpoint неуспешен: {response.status_code} - {response.text}")
            else:
                flashcards = response.json()
                print(f"✅ Flashcards endpoint работи: {len(flashcards)} флашкарти")
                if flashcards:
                    print(f"   Първа флашкарта: {flashcards[0]['front'][:50]}...")
        
        return doc_id
        
    except Exception as e:
        print(f"❌ Грешка: {e}")
        return None
    finally:
        # Cleanup
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

def test_cors():
    """Тества CORS настройките."""
    print("\n🌐 Тестване на CORS...")
    
    try:
        # Simulate browser preflight request
        headers = {
            'Origin': 'http://localhost:5174',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        
        response = requests.options(f"{BASE_URL}/api/documents/upload/", headers=headers)
        print(f"📊 OPTIONS заявка статус: {response.status_code}")
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
        }
        
        print("🔍 CORS Headers:")
        for header, value in cors_headers.items():
            print(f"   {header}: {value}")
        
        if cors_headers['Access-Control-Allow-Origin']:
            print("✅ CORS изглежда конфигуриран правилно")
        else:
            print("❌ CORS може да има проблеми")
            
    except Exception as e:
        print(f"❌ CORS тест грешка: {e}")

if __name__ == "__main__":
    print("🔍 Frontend API Testing")
    print("=" * 50)
    
    # Test CORS
    test_cors()
    
    # Test full workflow
    doc_id = test_upload_and_generate()
    
    if doc_id:
        print(f"\n🎉 Всички тестове минаха успешно!")
        print(f"📋 Документ ID за тестване във frontend: {doc_id}")
        print(f"🌐 Отворете http://localhost:5174 и тествайте с този документ")
    else:
        print(f"\n❌ Някои тестове не минаха. Проверете грешките по-горе.")
    
    print("\n🏁 Тестването завърши!")
