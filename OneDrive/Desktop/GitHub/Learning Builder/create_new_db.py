#!/usr/bin/env python
"""
Скрипт за създаване на нова база данни.
"""
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import os

def create_database():
    """Създава нова база данни."""
    
    # Настройки за връзка
    db_settings = {
        'host': '127.0.0.1',
        'port': '5432',
        'user': 'postgres-user',
        'password': 'password'
    }
    
    new_db_name = 'learning_builder_v2'
    
    try:
        # Свързване към PostgreSQL сървъра (не към конкретна база)
        conn = psycopg2.connect(
            host=db_settings['host'],
            port=db_settings['port'],
            user=db_settings['user'],
            password=db_settings['password'],
            database='postgres'  # Свързване към default база
        )
        
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Проверка дали базата съществува
        cursor.execute(f"SELECT 1 FROM pg_database WHERE datname = '{new_db_name}'")
        exists = cursor.fetchone()
        
        if exists:
            print(f"База данни '{new_db_name}' вече съществува. Изтривам я...")
            # Прекратяване на всички връзки към базата
            cursor.execute(f"""
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = '{new_db_name}' AND pid <> pg_backend_pid()
            """)
            # Изтриване на базата
            cursor.execute(f"DROP DATABASE {new_db_name}")
            print(f"База данни '{new_db_name}' е изтрита.")
        
        # Създаване на новата база
        cursor.execute(f"CREATE DATABASE {new_db_name}")
        print(f"База данни '{new_db_name}' е създадена успешно!")
        
        cursor.close()
        conn.close()
        
        return True
        
    except psycopg2.Error as e:
        print(f"Грешка при работа с базата данни: {e}")
        return False
    except Exception as e:
        print(f"Неочаквана грешка: {e}")
        return False

if __name__ == "__main__":
    print("🗄️ Създаване на нова база данни...")
    if create_database():
        print("✅ Базата данни е готова!")
    else:
        print("❌ Грешка при създаване на базата данни!")
