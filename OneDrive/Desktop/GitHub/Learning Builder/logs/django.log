INFO 2025-06-28 19:20:47,032 autoreload 16952 18216 Watching for file changes with StatReloader
INFO 2025-06-28 19:21:42,366 autoreload 9996 7952 Watching for file changes with StatReloader
INFO 2025-06-28 19:23:00,739 autoreload 9996 7952 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\Lib\site-packages\reportlab\lib\rl_accel.py changed, reloading.
INFO 2025-06-28 19:23:01,999 autoreload 21336 20260 Watching for file changes with StatReloader
INFO 2025-06-28 19:23:04,787 autoreload 21336 20260 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\Lib\site-packages\dotenv\__init__.py changed, reloading.
INFO 2025-06-28 19:23:06,072 autoreload 10304 7516 Watching for file changes with StatReloader
INFO 2025-06-28 19:23:12,240 autoreload 10304 7516 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\Lib\site-packages\drf_spectacular\generators.py changed, reloading.
INFO 2025-06-28 19:23:13,348 autoreload 10200 10896 Watching for file changes with StatReloader
INFO 2025-06-28 19:23:24,320 autoreload 14148 14304 Watching for file changes with StatReloader
INFO 2025-06-28 19:24:23,261 middleware 14148 15136 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2047", "upload_size": 1806}
INFO 2025-06-28 19:24:23,355 middleware 14148 15136 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 94.26, "response_size": 62, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:23,357 basehttp 14148 15136 "POST /api/documents/upload/ HTTP/1.1" 201 62
INFO 2025-06-28 19:24:23,376 middleware 14148 21164 {"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:24:23,395 middleware 14148 7112 {"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:24:23,426 middleware 14148 21164 {"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "status_code": 200, "duration_ms": 50.32, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:23,427 basehttp 14148 21164 "GET /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/ HTTP/1.1" 200 2
INFO 2025-06-28 19:24:23,438 middleware 14148 7112 {"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "status_code": 200, "duration_ms": 42.83, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:23,440 basehttp 14148 7112 "GET /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/ HTTP/1.1" 200 2
INFO 2025-06-28 19:24:26,618 middleware 14148 3604 {"event": "request_start", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
ERROR 2025-06-28 19:24:26,678 middleware 14148 3604 {"event": "request_complete", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/", "status_code": 500, "duration_ms": 60.09, "response_size": 92, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 19:24:26,679 log 14148 3604 Internal Server Error: /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/
ERROR 2025-06-28 19:24:26,680 basehttp 14148 3604 "POST /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/ HTTP/1.1" 500 92
INFO 2025-06-28 19:24:28,742 middleware 14148 20584 {"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:24:28,762 middleware 14148 18224 {"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:24:28,772 middleware 14148 20584 {"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "status_code": 200, "duration_ms": 30.08, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:28,773 basehttp 14148 20584 "GET /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/ HTTP/1.1" 200 2
INFO 2025-06-28 19:24:28,811 middleware 14148 18224 {"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "status_code": 200, "duration_ms": 49.32, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:28,813 basehttp 14148 18224 "GET /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/ HTTP/1.1" 200 2
INFO 2025-06-28 19:24:29,748 middleware 14148 21056 {"event": "request_start", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
ERROR 2025-06-28 19:24:29,799 middleware 14148 21056 {"event": "request_complete", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/", "status_code": 500, "duration_ms": 52.27, "response_size": 74, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 19:24:29,800 log 14148 21056 Internal Server Error: /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/
ERROR 2025-06-28 19:24:29,801 basehttp 14148 21056 "POST /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/ HTTP/1.1" 500 74
INFO 2025-06-28 19:24:44,665 middleware 14148 18756 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}
INFO 2025-06-28 19:24:44,721 middleware 14148 18756 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 57.42, "response_size": 62, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:44,722 basehttp 14148 18756 "POST /api/documents/upload/ HTTP/1.1" 201 62
INFO 2025-06-28 19:24:44,737 middleware 14148 7520 {"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:24:44,775 middleware 14148 7520 {"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "status_code": 200, "duration_ms": 37.99, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:24:44,777 basehttp 14148 7520 "GET /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/ HTTP/1.1" 200 2
INFO 2025-06-28 19:24:47,554 middleware 14148 20312 {"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
ERROR 2025-06-28 19:24:47,704 middleware 14148 20312 {"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "status_code": 500, "duration_ms": 150.63, "response_size": 92, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 19:24:47,704 log 14148 20312 Internal Server Error: /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/
ERROR 2025-06-28 19:24:47,710 basehttp 14148 20312 "POST /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/ HTTP/1.1" 500 92
INFO 2025-06-28 19:24:49,062 middleware 14148 19040 {"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
ERROR 2025-06-28 19:24:49,259 middleware 14148 19040 {"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "status_code": 500, "duration_ms": 197.0, "response_size": 92, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 19:24:49,260 log 14148 19040 Internal Server Error: /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/
ERROR 2025-06-28 19:24:49,261 basehttp 14148 19040 "POST /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/ HTTP/1.1" 500 92
INFO 2025-06-28 19:26:27,262 autoreload 14148 14304 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\tasks.py changed, reloading.
INFO 2025-06-28 19:26:28,443 autoreload 3496 16744 Watching for file changes with StatReloader
INFO 2025-06-28 19:26:48,249 autoreload 3496 16744 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 19:26:49,510 autoreload 11840 8744 Watching for file changes with StatReloader
INFO 2025-06-28 19:28:20,883 autoreload 11840 8744 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\ai\client.py changed, reloading.
INFO 2025-06-28 19:28:22,759 autoreload 15760 8416 Watching for file changes with StatReloader
INFO 2025-06-28 19:28:31,087 autoreload 15760 8416 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 19:28:32,796 autoreload 20712 9544 Watching for file changes with StatReloader
INFO 2025-06-28 19:28:47,820 autoreload 20712 9544 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\Lib\site-packages\openai\types\audio\transcription_create_params.py changed, reloading.
INFO 2025-06-28 19:28:49,730 autoreload 10800 5836 Watching for file changes with StatReloader
INFO 2025-06-28 19:29:12,526 autoreload 10800 5836 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\Lib\site-packages\httpx\__version__.py changed, reloading.
INFO 2025-06-28 19:29:15,000 autoreload 15444 4500 Watching for file changes with StatReloader
INFO 2025-06-28 19:30:04,229 middleware 15444 7484 {"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
INFO 2025-06-28 19:30:04,415 cache 15444 7484 Cache miss for flashcards with key ai_result:flashcards:bc67034f5423dadf
INFO 2025-06-28 19:30:09,554 autoreload 8976 3204 Watching for file changes with StatReloader
INFO 2025-06-28 19:30:10,205 cache 15444 7484 Cached flashcards result with key ai_result:flashcards:bc67034f5423dadf for 604800 seconds
INFO 2025-06-28 19:30:10,464 middleware 15444 7484 {"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "status_code": 200, "duration_ms": 6234.64, "response_size": 15, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:30:10,465 basehttp 15444 7484 "POST /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/ HTTP/1.1" 200 15
INFO 2025-06-28 19:30:10,480 middleware 15444 20436 {"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:30:10,522 middleware 15444 20436 {"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "status_code": 200, "duration_ms": 42.12, "response_size": 5124, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:30:10,527 basehttp 15444 20436 "GET /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/ HTTP/1.1" 200 5124
INFO 2025-06-28 19:30:31,351 middleware 15444 16992 {"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:30:31,352 middleware 15444 3620 {"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:30:31,382 middleware 15444 16992 {"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "status_code": 200, "duration_ms": 30.21, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:30:31,383 basehttp 15444 16992 "GET /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/ HTTP/1.1" 200 2
INFO 2025-06-28 19:30:31,397 middleware 15444 3620 {"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "status_code": 200, "duration_ms": 45.01, "response_size": 2, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:30:31,398 basehttp 15444 3620 "GET /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/ HTTP/1.1" 200 2
INFO 2025-06-28 19:30:32,411 middleware 15444 19268 {"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
INFO 2025-06-28 19:30:32,601 cache 15444 19268 Cache miss for questions with key ai_result:questions:abb5dfd3fb19bd4c
INFO 2025-06-28 19:30:48,883 cache 15444 19268 Cached questions result with key ai_result:questions:abb5dfd3fb19bd4c for 604800 seconds
INFO 2025-06-28 19:30:48,989 middleware 15444 19268 {"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/questions/", "status_code": 200, "duration_ms": 16577.25, "response_size": 15, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:30:48,990 basehttp 15444 19268 "POST /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/questions/ HTTP/1.1" 200 15
INFO 2025-06-28 19:30:49,003 middleware 15444 5808 {"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:30:49,032 middleware 15444 5808 {"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "status_code": 200, "duration_ms": 29.33, "response_size": 10638, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:30:49,034 basehttp 15444 5808 "GET /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/ HTTP/1.1" 200 10638
INFO 2025-06-28 19:33:22,466 middleware 15444 7140 {"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/pdf/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:33:22,576 middleware 15444 7140 {"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/pdf/", "status_code": 200, "duration_ms": 111.24, "response_size": 0, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:33:22,577 basehttp 15444 7140 "GET /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/pdf/ HTTP/1.1" 200 4271
INFO 2025-06-28 19:33:27,227 middleware 15444 4972 {"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/csv/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:33:27,263 middleware 15444 4972 {"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/csv/", "status_code": 200, "duration_ms": 35.83, "response_size": 0, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:33:27,266 basehttp 15444 4972 "GET /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/csv/ HTTP/1.1" 200 6347
INFO 2025-06-28 19:33:52,320 middleware 15444 19364 {"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/anki/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 19:33:52,455 middleware 15444 19364 {"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/anki/", "status_code": 200, "duration_ms": 134.81, "response_size": 0, "ip_address": "127.0.0.1"}
INFO 2025-06-28 19:33:52,456 basehttp 15444 19364 "GET /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/anki/ HTTP/1.1" 200 61658
INFO 2025-06-28 20:08:18,192 autoreload 8976 3204 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:08:18,657 autoreload 15444 4500 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:08:19,615 autoreload 14752 20296 Watching for file changes with StatReloader
INFO 2025-06-28 20:08:20,101 autoreload 20848 14108 Watching for file changes with StatReloader
INFO 2025-06-28 20:08:56,095 autoreload 14752 20296 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:08:56,153 autoreload 20848 14108 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:08:58,096 autoreload 19396 17192 Watching for file changes with StatReloader
INFO 2025-06-28 20:08:58,156 autoreload 12508 16996 Watching for file changes with StatReloader
INFO 2025-06-28 20:09:24,761 autoreload 12508 16996 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\serializers.py changed, reloading.
INFO 2025-06-28 20:09:24,765 autoreload 19396 17192 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\serializers.py changed, reloading.
INFO 2025-06-28 20:09:25,907 autoreload 6108 9724 Watching for file changes with StatReloader
INFO 2025-06-28 20:09:25,908 autoreload 10884 15820 Watching for file changes with StatReloader
INFO 2025-06-28 20:13:00,252 autoreload 6108 9724 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:13:00,280 autoreload 10884 15820 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:13:01,696 autoreload 13092 21260 Watching for file changes with StatReloader
INFO 2025-06-28 20:13:01,697 autoreload 8784 13236 Watching for file changes with StatReloader
INFO 2025-06-28 20:13:12,020 autoreload 13092 21260 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:13:12,052 autoreload 8784 13236 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:13:13,643 autoreload 5536 19752 Watching for file changes with StatReloader
INFO 2025-06-28 20:13:13,645 autoreload 14680 5760 Watching for file changes with StatReloader
INFO 2025-06-28 20:14:37,386 autoreload 5536 19752 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 20:14:37,399 autoreload 14680 5760 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 20:14:39,282 autoreload 9768 19532 Watching for file changes with StatReloader
INFO 2025-06-28 20:14:39,292 autoreload 20156 10832 Watching for file changes with StatReloader
INFO 2025-06-28 20:14:55,343 autoreload 20156 10832 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:14:55,355 autoreload 9768 19532 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:14:56,991 autoreload 14812 16652 Watching for file changes with StatReloader
INFO 2025-06-28 20:14:56,995 autoreload 16888 13556 Watching for file changes with StatReloader
INFO 2025-06-28 20:15:04,413 autoreload 14812 16652 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:15:04,464 autoreload 16888 13556 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:15:06,563 autoreload 10548 21060 Watching for file changes with StatReloader
INFO 2025-06-28 20:15:06,653 autoreload 12380 20384 Watching for file changes with StatReloader
INFO 2025-06-28 20:16:19,489 autoreload 12380 20384 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:16:19,530 autoreload 10548 21060 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\models.py changed, reloading.
INFO 2025-06-28 20:16:20,712 autoreload 8968 15180 Watching for file changes with StatReloader
INFO 2025-06-28 20:16:20,717 autoreload 5252 3396 Watching for file changes with StatReloader
INFO 2025-06-28 20:20:36,140 autoreload 5252 3396 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 20:20:36,141 autoreload 8968 15180 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 20:20:37,488 autoreload 17020 4860 Watching for file changes with StatReloader
INFO 2025-06-28 20:20:37,490 autoreload 5128 14656 Watching for file changes with StatReloader
INFO 2025-06-28 20:24:42,496 autoreload 17020 4860 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\urls.py changed, reloading.
INFO 2025-06-28 20:24:42,786 autoreload 5128 14656 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\urls.py changed, reloading.
INFO 2025-06-28 20:24:44,317 autoreload 13260 19380 Watching for file changes with StatReloader
INFO 2025-06-28 20:24:44,506 autoreload 12380 19836 Watching for file changes with StatReloader
INFO 2025-06-28 20:25:04,193 autoreload 13260 19380 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\urls.py changed, reloading.
INFO 2025-06-28 20:25:04,313 autoreload 12380 19836 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\urls.py changed, reloading.
INFO 2025-06-28 20:25:05,848 autoreload 13688 20900 Watching for file changes with StatReloader
INFO 2025-06-28 20:25:05,990 autoreload 13548 15832 Watching for file changes with StatReloader
INFO 2025-06-28 20:25:30,538 autoreload 10144 13324 Watching for file changes with StatReloader
INFO 2025-06-28 20:25:55,317 middleware 13688 16328 {"event": "request_start", "method": "GET", "path": "/api/docs/", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; bg-BG) WindowsPowerShell/5.1.26100.4484", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 20:25:55,333 middleware 13688 16328 {"event": "request_complete", "method": "GET", "path": "/api/docs/", "status_code": 200, "duration_ms": 15.99, "response_size": 4640, "ip_address": "127.0.0.1"}
INFO 2025-06-28 20:25:55,333 basehttp 13688 16328 "GET /api/docs/ HTTP/1.1" 200 4640
INFO 2025-06-28 20:25:56,070 basehttp 13688 16328 - Broken pipe from ('127.0.0.1', 60610)
INFO 2025-06-28 20:31:28,190 autoreload 11120 21468 Watching for file changes with StatReloader
INFO 2025-06-28 20:31:46,969 middleware 13688 15924 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}
WARNING 2025-06-28 20:31:46,998 middleware 13688 15924 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 31.04, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 20:31:46,998 log 13688 15924 Bad Request: /api/documents/upload/
WARNING 2025-06-28 20:31:46,999 basehttp 13688 15924 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 20:32:19,958 middleware 13688 17416 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}
WARNING 2025-06-28 20:32:19,964 middleware 13688 17416 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 5.69, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 20:32:19,964 log 13688 17416 Bad Request: /api/documents/upload/
WARNING 2025-06-28 20:32:19,968 basehttp 13688 17416 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 20:32:29,652 middleware 13688 15796 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2051", "upload_size": 1806}
WARNING 2025-06-28 20:32:29,657 middleware 13688 15796 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 6.53, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 20:32:29,660 log 13688 15796 Bad Request: /api/documents/upload/
WARNING 2025-06-28 20:32:29,661 basehttp 13688 15796 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 20:32:31,611 middleware 13688 20488 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2051", "upload_size": 1806}
WARNING 2025-06-28 20:32:31,618 middleware 13688 20488 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 7.0, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 20:32:31,619 log 13688 20488 Bad Request: /api/documents/upload/
WARNING 2025-06-28 20:32:31,619 basehttp 13688 20488 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 20:38:32,827 middleware 13688 3056 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 20:38:32,828 middleware 13688 3056 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295565BAAA0>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}
INFO 2025-06-28 20:38:32,851 middleware 13688 3240 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 20:38:32,853 middleware 13688 3240 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295565BB5B0>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:38:33,047 log 13688 3240 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295565BB5B0>.
ERROR 2025-06-28 20:38:33,050 log 13688 3056 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295565BAAA0>.
ERROR 2025-06-28 20:38:33,059 middleware 13688 3240 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 206.94, "response_size": 170572, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:38:33,061 basehttp 13688 3240 "GET /api/courses/ HTTP/1.1" 500 170572
ERROR 2025-06-28 20:38:33,062 middleware 13688 3056 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 233.94, "response_size": 170572, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:38:33,064 basehttp 13688 3056 "GET /api/courses/ HTTP/1.1" 500 170572
INFO 2025-06-28 20:39:17,035 middleware 13688 21460 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 20:39:17,037 middleware 13688 21460 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295564D9420>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}
INFO 2025-06-28 20:39:17,037 middleware 13688 4340 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 20:39:17,039 middleware 13688 6464 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 20:39:17,047 middleware 13688 4340 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000029556433D30>.", "duration_ms": 9.53, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:39:17,049 middleware 13688 6464 {"event": "request_exception", "method": "GET", "path": "/api/documents/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000029556701390>.", "duration_ms": 10.03, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:39:17,158 log 13688 4340 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000029556433D30>.
ERROR 2025-06-28 20:39:17,181 log 13688 21460 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295564D9420>.
ERROR 2025-06-28 20:39:17,187 middleware 13688 4340 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 149.84, "response_size": 170572, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:39:17,191 middleware 13688 21460 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 155.88, "response_size": 170572, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:39:17,196 basehttp 13688 4340 "GET /api/courses/ HTTP/1.1" 500 170572
ERROR 2025-06-28 20:39:17,205 basehttp 13688 21460 "GET /api/courses/ HTTP/1.1" 500 170572
ERROR 2025-06-28 20:39:17,219 log 13688 6464 Internal Server Error: /api/documents/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 203, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 66, in get_queryset
    queryset = Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000029556701390>.
ERROR 2025-06-28 20:39:17,227 middleware 13688 6464 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 500, "duration_ms": 187.94, "response_size": 170527, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:39:17,230 basehttp 13688 6464 "GET /api/documents/ HTTP/1.1" 500 170527
INFO 2025-06-28 20:42:24,144 middleware 13688 20808 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}
WARNING 2025-06-28 20:42:24,152 middleware 13688 20808 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 8.55, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 20:42:24,153 log 13688 20808 Bad Request: /api/documents/upload/
WARNING 2025-06-28 20:42:24,154 basehttp 13688 20808 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 20:45:27,927 autoreload 11120 21468 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 20:45:27,949 autoreload 10144 13324 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 20:45:27,998 autoreload 13688 20900 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 20:45:30,079 autoreload 15180 1608 Watching for file changes with StatReloader
INFO 2025-06-28 20:45:30,083 autoreload 3360 19020 Watching for file changes with StatReloader
INFO 2025-06-28 20:45:30,115 autoreload 21380 15636 Watching for file changes with StatReloader
INFO 2025-06-28 20:45:47,961 autoreload 21376 19984 Watching for file changes with StatReloader
INFO 2025-06-28 20:46:57,720 middleware 3360 18012 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 20:46:57,721 middleware 3360 17432 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 20:46:57,721 middleware 3360 10548 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 20:46:57,721 middleware 3360 14152 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 20:46:57,722 middleware 3360 4820 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 20:46:57,724 middleware 3360 7528 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 20:46:57,733 middleware 3360 18012 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E285E0>.", "duration_ms": 12.99, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:57,733 middleware 3360 17432 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E2BD00>.", "duration_ms": 12.0, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:57,734 middleware 3360 14152 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E646D0>.", "duration_ms": 12.99, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:57,735 middleware 3360 10548 {"event": "request_exception", "method": "GET", "path": "/api/documents/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E65000>.", "duration_ms": 14.0, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:57,735 middleware 3360 4820 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E65A50>.", "duration_ms": 13.0, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:57,736 middleware 3360 7528 {"event": "request_exception", "method": "GET", "path": "/api/documents/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E66380>.", "duration_ms": 12.0, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:58,017 log 3360 18012 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E285E0>.
ERROR 2025-06-28 20:46:58,034 log 3360 4820 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E65A50>.
ERROR 2025-06-28 20:46:58,068 log 3360 14152 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E646D0>.
ERROR 2025-06-28 20:46:58,072 log 3360 10548 Internal Server Error: /api/documents/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 203, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 66, in get_queryset
    queryset = Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E65000>.
ERROR 2025-06-28 20:46:58,076 log 3360 7528 Internal Server Error: /api/documents/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 203, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 66, in get_queryset
    queryset = Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E66380>.
ERROR 2025-06-28 20:46:58,094 log 3360 17432 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E2BD00>.
ERROR 2025-06-28 20:46:58,105 middleware 3360 18012 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 385.13, "response_size": 171328, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:58,108 middleware 3360 4820 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 386.15, "response_size": 171328, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:58,116 middleware 3360 14152 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 394.14, "response_size": 171328, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:58,119 basehttp 3360 18012 "GET /api/courses/ HTTP/1.1" 500 171328
ERROR 2025-06-28 20:46:58,119 basehttp 3360 4820 "GET /api/courses/ HTTP/1.1" 500 171328
ERROR 2025-06-28 20:46:58,120 middleware 3360 10548 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 500, "duration_ms": 399.15, "response_size": 171283, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:58,123 middleware 3360 7528 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 500, "duration_ms": 399.14, "response_size": 171283, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:58,125 basehttp 3360 14152 "GET /api/courses/ HTTP/1.1" 500 171328
ERROR 2025-06-28 20:46:58,125 middleware 3360 17432 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 404.14, "response_size": 171328, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 20:46:58,128 basehttp 3360 10548 "GET /api/documents/ HTTP/1.1" 500 171283
ERROR 2025-06-28 20:46:58,129 basehttp 3360 7528 "GET /api/documents/ HTTP/1.1" 500 171283
ERROR 2025-06-28 20:46:58,132 basehttp 3360 17432 "GET /api/courses/ HTTP/1.1" 500 171328
INFO 2025-06-28 20:47:05,992 middleware 3360 276 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}
WARNING 2025-06-28 20:47:06,018 middleware 3360 276 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 26.02, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 20:47:06,020 log 3360 276 Bad Request: /api/documents/upload/
WARNING 2025-06-28 20:47:06,020 basehttp 3360 276 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 20:49:24,158 autoreload 21380 15636 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:49:24,659 autoreload 21376 19984 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:49:24,994 autoreload 3360 19020 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:49:25,973 autoreload 18912 13580 Watching for file changes with StatReloader
INFO 2025-06-28 20:49:26,394 autoreload 5924 6932 Watching for file changes with StatReloader
INFO 2025-06-28 20:49:26,673 autoreload 3836 17488 Watching for file changes with StatReloader
INFO 2025-06-28 20:49:37,510 autoreload 18912 13580 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:49:37,856 autoreload 5924 6932 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:49:38,025 autoreload 3836 17488 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:49:38,833 autoreload 14156 17556 Watching for file changes with StatReloader
INFO 2025-06-28 20:49:39,216 autoreload 1516 15572 Watching for file changes with StatReloader
INFO 2025-06-28 20:49:39,421 autoreload 20548 1944 Watching for file changes with StatReloader
INFO 2025-06-28 20:49:52,784 autoreload 1516 15572 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:49:53,001 autoreload 14156 17556 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:49:53,033 autoreload 20548 1944 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:49:56,523 autoreload 18744 12780 Watching for file changes with StatReloader
INFO 2025-06-28 20:49:56,534 autoreload 11072 15824 Watching for file changes with StatReloader
INFO 2025-06-28 20:49:56,549 autoreload 6888 16696 Watching for file changes with StatReloader
INFO 2025-06-28 20:50:08,270 autoreload 11072 15824 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:50:08,309 autoreload 18744 12780 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:50:08,359 autoreload 6888 16696 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:50:09,709 autoreload 14540 13104 Watching for file changes with StatReloader
INFO 2025-06-28 20:50:09,712 autoreload 20844 19804 Watching for file changes with StatReloader
INFO 2025-06-28 20:50:09,745 autoreload 8468 16628 Watching for file changes with StatReloader
INFO 2025-06-28 20:50:19,282 autoreload 20844 19804 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:50:19,297 autoreload 14540 13104 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:50:19,339 autoreload 8468 16628 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:50:20,967 autoreload 17052 2372 Watching for file changes with StatReloader
INFO 2025-06-28 20:50:20,985 autoreload 19620 16024 Watching for file changes with StatReloader
INFO 2025-06-28 20:50:21,011 autoreload 18188 948 Watching for file changes with StatReloader
INFO 2025-06-28 20:50:30,418 autoreload 17052 2372 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:50:30,425 autoreload 19620 16024 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:50:30,455 autoreload 18188 948 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:50:31,996 autoreload 12588 18548 Watching for file changes with StatReloader
INFO 2025-06-28 20:50:32,003 autoreload 19364 18688 Watching for file changes with StatReloader
INFO 2025-06-28 20:50:32,020 autoreload 16932 15748 Watching for file changes with StatReloader
INFO 2025-06-28 20:50:40,359 autoreload 19364 18688 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:50:40,383 autoreload 12588 18548 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:50:40,387 autoreload 16932 15748 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:50:42,245 autoreload 18504 2644 Watching for file changes with StatReloader
INFO 2025-06-28 20:50:42,308 autoreload 12508 9064 Watching for file changes with StatReloader
INFO 2025-06-28 20:50:42,367 autoreload 5188 19312 Watching for file changes with StatReloader
INFO 2025-06-28 20:51:29,220 middleware 18504 9208 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 20:51:29,285 middleware 18504 9208 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 64.93, "response_size": 606, "ip_address": "127.0.0.1"}
INFO 2025-06-28 20:51:29,287 basehttp 18504 9208 "GET /api/documents/ HTTP/1.1" 200 606
INFO 2025-06-28 20:51:29,311 middleware 18504 15540 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "350", "upload_size": 74}
WARNING 2025-06-28 20:51:29,342 middleware 18504 15540 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 32.2, "response_size": 77, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 20:51:29,343 log 18504 15540 Bad Request: /api/documents/upload/
WARNING 2025-06-28 20:51:29,344 basehttp 18504 15540 "POST /api/documents/upload/ HTTP/1.1" 400 77
INFO 2025-06-28 20:51:53,280 middleware 18504 18972 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 20:51:53,317 middleware 18504 18972 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 36.42, "response_size": 606, "ip_address": "127.0.0.1"}
INFO 2025-06-28 20:51:53,318 basehttp 18504 18972 "GET /api/documents/ HTTP/1.1" 200 606
INFO 2025-06-28 20:51:53,337 middleware 18504 7420 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "779", "upload_size": 503}
INFO 2025-06-28 20:51:53,403 middleware 18504 7420 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 66.78, "response_size": 410, "ip_address": "127.0.0.1"}
INFO 2025-06-28 20:51:53,404 basehttp 18504 7420 "POST /api/documents/upload/ HTTP/1.1" 201 410
INFO 2025-06-28 20:51:53,412 middleware 18504 17868 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 20:51:53,461 middleware 18504 17868 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 48.97, "response_size": 1017, "ip_address": "127.0.0.1"}
INFO 2025-06-28 20:51:53,462 basehttp 18504 17868 "GET /api/documents/ HTTP/1.1" 200 1017
INFO 2025-06-28 20:59:22,492 autoreload 18504 2644 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:59:22,828 autoreload 12508 9064 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:59:23,386 autoreload 5188 19312 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:59:24,537 autoreload 15608 440 Watching for file changes with StatReloader
INFO 2025-06-28 20:59:24,716 autoreload 20492 19776 Watching for file changes with StatReloader
INFO 2025-06-28 20:59:25,122 autoreload 7228 17812 Watching for file changes with StatReloader
INFO 2025-06-28 20:59:26,646 middleware 15608 15852 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}
WARNING 2025-06-28 20:59:26,675 middleware 15608 15852 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 28.99, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 20:59:26,676 log 15608 15852 Bad Request: /api/documents/upload/
WARNING 2025-06-28 20:59:26,677 basehttp 15608 15852 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 20:59:34,463 autoreload 20492 19776 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:59:34,664 autoreload 15608 440 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:59:34,816 autoreload 7228 17812 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 20:59:37,488 autoreload 18996 20212 Watching for file changes with StatReloader
INFO 2025-06-28 20:59:37,514 autoreload 21140 6196 Watching for file changes with StatReloader
INFO 2025-06-28 20:59:37,614 autoreload 8236 21340 Watching for file changes with StatReloader
INFO 2025-06-28 20:59:46,914 autoreload 18996 20212 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 20:59:47,067 autoreload 8236 21340 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 20:59:47,224 autoreload 21140 6196 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 20:59:50,460 autoreload 12800 19428 Watching for file changes with StatReloader
INFO 2025-06-28 20:59:50,569 autoreload 20940 7328 Watching for file changes with StatReloader
INFO 2025-06-28 20:59:50,700 autoreload 8676 19772 Watching for file changes with StatReloader
INFO 2025-06-28 21:00:10,045 autoreload 5088 2776 Watching for file changes with StatReloader
INFO 2025-06-28 21:00:40,096 autoreload 5088 2776 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:00:40,618 autoreload 20940 7328 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:00:40,696 autoreload 8676 19772 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:00:42,720 autoreload 20384 14800 Watching for file changes with StatReloader
INFO 2025-06-28 21:00:43,409 autoreload 14140 11632 Watching for file changes with StatReloader
INFO 2025-06-28 21:00:43,491 autoreload 21356 7196 Watching for file changes with StatReloader
INFO 2025-06-28 21:00:53,129 autoreload 20384 14800 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\urls.py changed, reloading.
INFO 2025-06-28 21:00:53,815 autoreload 14140 11632 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\urls.py changed, reloading.
INFO 2025-06-28 21:00:53,900 autoreload 21356 7196 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\urls.py changed, reloading.
INFO 2025-06-28 21:00:55,921 autoreload 19308 8924 Watching for file changes with StatReloader
INFO 2025-06-28 21:00:56,202 autoreload 20936 15508 Watching for file changes with StatReloader
INFO 2025-06-28 21:00:56,281 autoreload 19292 10504 Watching for file changes with StatReloader
INFO 2025-06-28 21:01:04,012 autoreload 19308 8924 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\urls.py changed, reloading.
INFO 2025-06-28 21:01:04,539 autoreload 19292 10504 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\urls.py changed, reloading.
INFO 2025-06-28 21:01:04,591 autoreload 20936 15508 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\urls.py changed, reloading.
INFO 2025-06-28 21:01:06,622 autoreload 9064 12508 Watching for file changes with StatReloader
INFO 2025-06-28 21:01:07,113 autoreload 4368 16584 Watching for file changes with StatReloader
INFO 2025-06-28 21:01:07,122 autoreload 15976 4436 Watching for file changes with StatReloader
INFO 2025-06-28 21:02:41,179 middleware 9064 18808 {"event": "request_start", "method": "OPTIONS", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0"}
INFO 2025-06-28 21:02:41,180 middleware 9064 18808 {"event": "request_complete", "method": "OPTIONS", "path": "/api/documents/upload/", "status_code": 200, "duration_ms": 0.99, "response_size": 0, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:02:41,181 basehttp 9064 18808 "OPTIONS /api/documents/upload/ HTTP/1.1" 200 0
INFO 2025-06-28 21:02:41,204 middleware 9064 7592 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "1202", "upload_size": 905}
INFO 2025-06-28 21:02:41,296 middleware 9064 7592 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 92.02, "response_size": 431, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:02:41,296 basehttp 9064 7592 "POST /api/documents/upload/ HTTP/1.1" 201 431
INFO 2025-06-28 21:02:41,308 middleware 9064 13624 {"event": "request_start", "method": "POST", "path": "/api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/flashcards/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
ERROR 2025-06-28 21:02:42,269 middleware 9064 13624 {"event": "request_complete", "method": "POST", "path": "/api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/flashcards/", "status_code": 500, "duration_ms": 961.22, "response_size": 225, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:02:42,269 log 9064 13624 Internal Server Error: /api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/flashcards/
ERROR 2025-06-28 21:02:42,274 basehttp 9064 13624 "POST /api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/flashcards/ HTTP/1.1" 500 225
INFO 2025-06-28 21:02:42,278 middleware 9064 20384 {"event": "request_start", "method": "POST", "path": "/api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/quiz/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
ERROR 2025-06-28 21:02:42,286 middleware 9064 20384 {"event": "request_complete", "method": "POST", "path": "/api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/quiz/", "status_code": 500, "duration_ms": 8.18, "response_size": 215, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:02:42,290 log 9064 20384 Internal Server Error: /api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/quiz/
ERROR 2025-06-28 21:02:42,290 basehttp 9064 20384 "POST /api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/quiz/ HTTP/1.1" 500 215
INFO 2025-06-28 21:02:42,299 middleware 9064 9000 {"event": "request_start", "method": "GET", "path": "/api/study-sets/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:02:42,305 middleware 9064 9000 {"event": "request_exception", "method": "GET", "path": "/api/study-sets/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B864909BD0>.", "duration_ms": 6.72, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:02:42,446 log 9064 9000 Internal Server Error: /api/study-sets/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 203, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 115, in get_queryset
    queryset = StudySet.objects.filter(document__owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B864909BD0>.
ERROR 2025-06-28 21:02:42,462 middleware 9064 9000 {"event": "request_complete", "method": "GET", "path": "/api/study-sets/", "status_code": 500, "duration_ms": 162.94, "response_size": 170417, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:02:42,467 basehttp 9064 9000 "GET /api/study-sets/?document=9b174874-1861-4fa8-94d8-c82f9fbf4265 HTTP/1.1" 500 170417
INFO 2025-06-28 21:04:00,095 autoreload 4368 16584 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:04:00,215 autoreload 9064 12508 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:04:00,456 autoreload 15976 4436 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:04:03,211 autoreload 14544 15684 Watching for file changes with StatReloader
INFO 2025-06-28 21:04:03,252 autoreload 11396 18440 Watching for file changes with StatReloader
INFO 2025-06-28 21:04:03,252 autoreload 12000 17972 Watching for file changes with StatReloader
INFO 2025-06-28 21:04:11,715 autoreload 12000 17972 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:04:11,762 autoreload 11396 18440 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:04:11,782 autoreload 14544 15684 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:04:14,332 autoreload 21224 10092 Watching for file changes with StatReloader
INFO 2025-06-28 21:04:14,333 autoreload 14264 20888 Watching for file changes with StatReloader
INFO 2025-06-28 21:04:14,334 autoreload 14152 21164 Watching for file changes with StatReloader
INFO 2025-06-28 21:04:22,388 autoreload 14264 20888 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:04:22,389 autoreload 14152 21164 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:04:22,431 autoreload 21224 10092 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:04:25,803 autoreload 17112 21460 Watching for file changes with StatReloader
INFO 2025-06-28 21:04:25,950 autoreload 19152 20984 Watching for file changes with StatReloader
INFO 2025-06-28 21:04:26,010 autoreload 21328 16628 Watching for file changes with StatReloader
INFO 2025-06-28 21:04:32,797 middleware 17112 19480 {"event": "request_start", "method": "OPTIONS", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0"}
INFO 2025-06-28 21:04:32,798 middleware 17112 19480 {"event": "request_complete", "method": "OPTIONS", "path": "/api/documents/upload/", "status_code": 200, "duration_ms": 1.0, "response_size": 0, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:04:32,799 basehttp 17112 19480 "OPTIONS /api/documents/upload/ HTTP/1.1" 200 0
INFO 2025-06-28 21:04:32,808 middleware 17112 2776 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "1202", "upload_size": 905}
WARNING 2025-06-28 21:04:32,842 middleware 17112 2776 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 403, "duration_ms": 34.16, "response_size": 63, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 21:04:32,844 log 17112 2776 Forbidden: /api/documents/upload/
WARNING 2025-06-28 21:04:32,845 basehttp 17112 2776 "POST /api/documents/upload/ HTTP/1.1" 403 63
INFO 2025-06-28 21:04:52,445 autoreload 21328 16628 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 21:04:52,480 autoreload 17112 21460 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 21:04:53,402 autoreload 19152 20984 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 21:04:54,993 autoreload 20988 16364 Watching for file changes with StatReloader
INFO 2025-06-28 21:04:54,995 autoreload 19052 8008 Watching for file changes with StatReloader
INFO 2025-06-28 21:04:56,130 autoreload 19888 7504 Watching for file changes with StatReloader
INFO 2025-06-28 21:05:11,416 autoreload 21384 15308 Watching for file changes with StatReloader
INFO 2025-06-28 21:05:26,219 middleware 19052 20592 {"event": "request_start", "method": "OPTIONS", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0"}
INFO 2025-06-28 21:05:26,221 middleware 19052 20592 {"event": "request_complete", "method": "OPTIONS", "path": "/api/documents/upload/", "status_code": 200, "duration_ms": 1.99, "response_size": 0, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:05:26,222 basehttp 19052 20592 "OPTIONS /api/documents/upload/ HTTP/1.1" 200 0
INFO 2025-06-28 21:05:26,232 middleware 19052 18504 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "1202", "upload_size": 905}
WARNING 2025-06-28 21:05:26,270 middleware 19052 18504 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 403, "duration_ms": 38.11, "response_size": 63, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 21:05:26,272 log 19052 18504 Forbidden: /api/documents/upload/
WARNING 2025-06-28 21:05:26,279 basehttp 19052 18504 "POST /api/documents/upload/ HTTP/1.1" 403 63
INFO 2025-06-28 21:05:40,321 autoreload 20988 16364 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:05:40,446 autoreload 21384 15308 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:05:40,614 autoreload 19052 8008 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:05:42,234 autoreload 16340 19616 Watching for file changes with StatReloader
INFO 2025-06-28 21:05:42,521 autoreload 19300 17760 Watching for file changes with StatReloader
INFO 2025-06-28 21:05:42,753 autoreload 11132 8780 Watching for file changes with StatReloader
INFO 2025-06-28 21:05:48,927 middleware 16340 10736 {"event": "request_start", "method": "OPTIONS", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0"}
INFO 2025-06-28 21:05:48,928 middleware 16340 10736 {"event": "request_complete", "method": "OPTIONS", "path": "/api/documents/upload/", "status_code": 200, "duration_ms": 2.02, "response_size": 0, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:05:48,930 basehttp 16340 10736 "OPTIONS /api/documents/upload/ HTTP/1.1" 200 0
INFO 2025-06-28 21:05:48,946 middleware 16340 21156 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "1202", "upload_size": 905}
INFO 2025-06-28 21:05:49,022 middleware 16340 21156 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 76.97, "response_size": 439, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:05:49,024 basehttp 16340 21156 "POST /api/documents/upload/ HTTP/1.1" 201 439
INFO 2025-06-28 21:05:49,031 middleware 16340 8476 {"event": "request_start", "method": "POST", "path": "/api/documents/962c7fb0-53ff-4efa-b8e1-f84e40b967ef/generate/flashcards/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
INFO 2025-06-28 21:05:50,193 cache 16340 8476 Cache miss for flashcards with key ai_result:flashcards:a31c2dcc858f8cff
INFO 2025-06-28 21:05:58,782 cache 16340 8476 Cached flashcards result with key ai_result:flashcards:a31c2dcc858f8cff for 604800 seconds
INFO 2025-06-28 21:05:58,882 middleware 16340 8476 {"event": "request_complete", "method": "POST", "path": "/api/documents/962c7fb0-53ff-4efa-b8e1-f84e40b967ef/generate/flashcards/", "status_code": 200, "duration_ms": 9850.98, "response_size": 96, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:05:58,885 basehttp 16340 8476 "POST /api/documents/962c7fb0-53ff-4efa-b8e1-f84e40b967ef/generate/flashcards/ HTTP/1.1" 200 96
INFO 2025-06-28 21:05:58,892 middleware 16340 13908 {"event": "request_start", "method": "POST", "path": "/api/documents/962c7fb0-53ff-4efa-b8e1-f84e40b967ef/generate/quiz/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}
INFO 2025-06-28 21:05:58,959 cache 16340 13908 Cache miss for questions with key ai_result:questions:1ca776486586604a
INFO 2025-06-28 21:06:10,329 cache 16340 13908 Cached questions result with key ai_result:questions:1ca776486586604a for 604800 seconds
INFO 2025-06-28 21:06:10,440 middleware 16340 13908 {"event": "request_complete", "method": "POST", "path": "/api/documents/962c7fb0-53ff-4efa-b8e1-f84e40b967ef/generate/quiz/", "status_code": 200, "duration_ms": 11548.0, "response_size": 144, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:06:10,441 basehttp 16340 13908 "POST /api/documents/962c7fb0-53ff-4efa-b8e1-f84e40b967ef/generate/quiz/ HTTP/1.1" 200 144
INFO 2025-06-28 21:06:10,446 middleware 16340 18172 {"event": "request_start", "method": "GET", "path": "/api/study-sets/b147fce2-8bfe-44bc-be25-f5bc13d59599/quiz/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:06:10,523 middleware 16340 18172 {"event": "request_complete", "method": "GET", "path": "/api/study-sets/b147fce2-8bfe-44bc-be25-f5bc13d59599/quiz/", "status_code": 200, "duration_ms": 76.14, "response_size": 10160, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:06:10,524 basehttp 16340 18172 "GET /api/study-sets/b147fce2-8bfe-44bc-be25-f5bc13d59599/quiz/ HTTP/1.1" 200 10160
INFO 2025-06-28 21:06:10,529 middleware 16340 276 {"event": "request_start", "method": "GET", "path": "/api/study-sets/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:06:10,595 middleware 16340 276 {"event": "request_complete", "method": "GET", "path": "/api/study-sets/", "status_code": 200, "duration_ms": 66.31, "response_size": 3704, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:06:10,597 basehttp 16340 276 "GET /api/study-sets/?document=962c7fb0-53ff-4efa-b8e1-f84e40b967ef HTTP/1.1" 200 3704
INFO 2025-06-28 21:06:10,601 middleware 16340 14656 {"event": "request_start", "method": "GET", "path": "/api/study-sets/49d1142b-fb03-4d25-8e32-941045f78774/flashcards/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:06:10,643 middleware 16340 14656 {"event": "request_complete", "method": "GET", "path": "/api/study-sets/49d1142b-fb03-4d25-8e32-941045f78774/flashcards/", "status_code": 200, "duration_ms": 42.28, "response_size": 5727, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:06:10,646 basehttp 16340 14656 "GET /api/study-sets/49d1142b-fb03-4d25-8e32-941045f78774/flashcards/ HTTP/1.1" 200 5727
INFO 2025-06-28 21:07:26,010 middleware 16340 7504 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}
WARNING 2025-06-28 21:07:26,013 middleware 16340 7504 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 4.01, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 21:07:26,014 log 16340 7504 Bad Request: /api/documents/upload/
WARNING 2025-06-28 21:07:26,015 basehttp 16340 7504 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 21:07:28,546 middleware 16340 11124 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:07:28,548 middleware 16340 10024 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:07:28,549 middleware 16340 7856 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:07:28,551 middleware 16340 7216 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:07:28,553 middleware 16340 11124 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D135DB0>.", "duration_ms": 7.0, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:07:28,554 middleware 16340 21196 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:07:28,554 middleware 16340 10024 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D313B50>.", "duration_ms": 7.08, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:07:28,556 middleware 16340 7856 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D2CE170>.", "duration_ms": 8.1, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:07:28,557 middleware 16340 7216 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D4365C0>.", "duration_ms": 6.11, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:07:28,570 middleware 16340 10756 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:07:28,613 middleware 16340 21196 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 59.13, "response_size": 1886, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:07:28,624 basehttp 16340 21196 "GET /api/documents/ HTTP/1.1" 200 1886
INFO 2025-06-28 21:07:28,647 middleware 16340 10756 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 76.87, "response_size": 1886, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:07:28,649 basehttp 16340 10756 "GET /api/documents/ HTTP/1.1" 200 1886
ERROR 2025-06-28 21:07:28,739 log 16340 7216 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D4365C0>.
ERROR 2025-06-28 21:07:28,745 log 16340 10024 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D313B50>.
ERROR 2025-06-28 21:07:28,755 log 16340 11124 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D135DB0>.
ERROR 2025-06-28 21:07:28,782 middleware 16340 7216 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 231.77, "response_size": 171420, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:07:28,783 log 16340 7856 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D2CE170>.
ERROR 2025-06-28 21:07:28,787 middleware 16340 10024 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 239.77, "response_size": 171420, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:07:28,789 basehttp 16340 7216 "GET /api/courses/ HTTP/1.1" 500 171420
ERROR 2025-06-28 21:07:28,790 basehttp 16340 10024 "GET /api/courses/ HTTP/1.1" 500 171420
ERROR 2025-06-28 21:07:28,791 middleware 16340 11124 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 245.79, "response_size": 171420, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:07:28,794 middleware 16340 7856 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 246.78, "response_size": 171420, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:07:28,798 basehttp 16340 11124 "GET /api/courses/ HTTP/1.1" 500 171420
ERROR 2025-06-28 21:07:28,800 basehttp 16340 7856 "GET /api/courses/ HTTP/1.1" 500 171420
INFO 2025-06-28 21:07:34,614 middleware 16340 20844 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2051", "upload_size": 1806}
WARNING 2025-06-28 21:07:34,617 middleware 16340 20844 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 3.97, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 21:07:34,618 log 16340 20844 Bad Request: /api/documents/upload/
WARNING 2025-06-28 21:07:34,619 basehttp 16340 20844 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 21:07:35,830 middleware 16340 17512 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2049", "upload_size": 1806}
WARNING 2025-06-28 21:07:35,832 middleware 16340 17512 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 3.0, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 21:07:35,833 log 16340 17512 Bad Request: /api/documents/upload/
WARNING 2025-06-28 21:07:35,834 basehttp 16340 17512 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 21:07:46,898 middleware 16340 10832 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2051", "upload_size": 1806}
WARNING 2025-06-28 21:07:46,900 middleware 16340 10832 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 3.02, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 21:07:46,901 log 16340 10832 Bad Request: /api/documents/upload/
WARNING 2025-06-28 21:07:46,902 basehttp 16340 10832 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 21:08:49,275 middleware 16340 6728 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:08:49,277 middleware 16340 8544 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:08:49,278 middleware 16340 6728 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2B7BD210>.", "duration_ms": 3.0, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:08:49,304 middleware 16340 6484 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:08:49,305 middleware 16340 19092 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:08:49,306 middleware 16340 1628 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:08:49,307 middleware 16340 20504 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:08:49,309 middleware 16340 6484 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2DD4DBA0>.", "duration_ms": 4.63, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:08:49,311 middleware 16340 19092 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2DD4CE80>.", "duration_ms": 5.78, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:08:49,312 middleware 16340 1628 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2DD4EC20>.", "duration_ms": 6.38, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:08:49,320 middleware 16340 8544 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 43.78, "response_size": 1886, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:08:49,355 basehttp 16340 8544 "GET /api/documents/ HTTP/1.1" 200 1886
INFO 2025-06-28 21:08:49,363 middleware 16340 20504 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 55.71, "response_size": 1886, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:08:49,370 basehttp 16340 20504 "GET /api/documents/ HTTP/1.1" 200 1886
ERROR 2025-06-28 21:08:49,404 log 16340 6728 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2B7BD210>.
ERROR 2025-06-28 21:08:49,413 middleware 16340 6728 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 137.83, "response_size": 171420, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:08:49,424 basehttp 16340 6728 "GET /api/courses/ HTTP/1.1" 500 171420
ERROR 2025-06-28 21:08:49,531 log 16340 1628 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2DD4EC20>.
ERROR 2025-06-28 21:08:49,535 log 16340 19092 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2DD4CE80>.
ERROR 2025-06-28 21:08:49,564 log 16340 6484 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2DD4DBA0>.
ERROR 2025-06-28 21:08:49,564 middleware 16340 1628 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 258.66, "response_size": 171420, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:08:49,569 middleware 16340 19092 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 264.7, "response_size": 171420, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:08:49,573 basehttp 16340 1628 "GET /api/courses/ HTTP/1.1" 500 171420
ERROR 2025-06-28 21:08:49,574 middleware 16340 6484 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 270.25, "response_size": 171420, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:08:49,576 basehttp 16340 19092 "GET /api/courses/ HTTP/1.1" 500 171420
ERROR 2025-06-28 21:08:49,578 basehttp 16340 6484 "GET /api/courses/ HTTP/1.1" 500 171420
INFO 2025-06-28 21:08:57,086 autoreload 19300 17760 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 21:08:57,375 autoreload 11132 8780 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 21:08:57,470 autoreload 16340 19616 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\Learning_Builder\settings.py changed, reloading.
INFO 2025-06-28 21:08:58,722 autoreload 9900 17792 Watching for file changes with StatReloader
INFO 2025-06-28 21:08:59,007 autoreload 18476 15616 Watching for file changes with StatReloader
INFO 2025-06-28 21:08:59,186 autoreload 9348 5444 Watching for file changes with StatReloader
INFO 2025-06-28 21:09:12,859 autoreload 17848 13964 Watching for file changes with StatReloader
INFO 2025-06-28 21:09:18,941 middleware 18476 3016 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}
WARNING 2025-06-28 21:09:18,948 middleware 18476 3016 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 8.01, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 21:09:18,950 log 18476 3016 Bad Request: /api/documents/upload/
WARNING 2025-06-28 21:09:18,951 basehttp 18476 3016 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 21:09:56,942 middleware 18476 19092 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; bg-BG) WindowsPowerShell/5.1.26100.4484", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:09:57,000 middleware 18476 19092 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 58.27, "response_size": 1886, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:09:57,001 basehttp 18476 19092 "GET /api/documents/ HTTP/1.1" 200 1886
INFO 2025-06-28 21:10:12,207 middleware 18476 6840 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:10:12,208 middleware 18476 20260 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:10:12,211 middleware 18476 6840 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E45F6C670>.", "duration_ms": 3.66, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:10:12,212 middleware 18476 4500 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:10:12,215 middleware 18476 20260 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E45F6C8B0>.", "duration_ms": 6.36, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:10:12,215 middleware 18476 10712 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:10:12,217 middleware 18476 8496 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:10:12,291 middleware 18476 4500 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E46029510>.", "duration_ms": 78.79, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:10:12,295 middleware 18476 10548 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:10:12,305 middleware 18476 10712 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E45EE2F20>.", "duration_ms": 89.56, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:10:12,357 middleware 18476 8496 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 139.88, "response_size": 1886, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:10:12,367 basehttp 18476 8496 "GET /api/documents/ HTTP/1.1" 200 1886
INFO 2025-06-28 21:10:12,371 middleware 18476 10548 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 76.07, "response_size": 1886, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:10:12,384 basehttp 18476 10548 "GET /api/documents/ HTTP/1.1" 200 1886
ERROR 2025-06-28 21:10:12,555 log 18476 6840 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E45F6C670>.
ERROR 2025-06-28 21:10:12,576 log 18476 10712 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E45EE2F20>.
ERROR 2025-06-28 21:10:12,583 log 18476 4500 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E46029510>.
ERROR 2025-06-28 21:10:12,586 log 18476 20260 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E45F6C8B0>.
ERROR 2025-06-28 21:10:12,592 middleware 18476 6840 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 384.83, "response_size": 170877, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:10:12,595 middleware 18476 10712 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 380.07, "response_size": 170877, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:10:12,599 middleware 18476 4500 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 386.59, "response_size": 170877, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:10:12,602 basehttp 18476 6840 "GET /api/courses/ HTTP/1.1" 500 170877
ERROR 2025-06-28 21:10:12,602 basehttp 18476 4500 "GET /api/courses/ HTTP/1.1" 500 170877
ERROR 2025-06-28 21:10:12,602 middleware 18476 20260 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 393.33, "response_size": 170877, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:10:12,602 basehttp 18476 10712 "GET /api/courses/ HTTP/1.1" 500 170877
ERROR 2025-06-28 21:10:12,610 basehttp 18476 20260 "GET /api/courses/ HTTP/1.1" 500 170877
INFO 2025-06-28 21:10:26,073 middleware 18476 21224 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}
WARNING 2025-06-28 21:10:26,076 middleware 18476 21224 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 4.06, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 21:10:26,077 log 18476 21224 Bad Request: /api/documents/upload/
WARNING 2025-06-28 21:10:26,079 basehttp 18476 21224 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 21:10:41,446 middleware 18476 17492 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:10:41,487 middleware 18476 17492 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 40.77, "response_size": 1886, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:10:41,489 basehttp 18476 17492 "GET /api/documents/ HTTP/1.1" 200 1886
INFO 2025-06-28 21:10:41,523 middleware 18476 18448 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "1197", "upload_size": 910}
INFO 2025-06-28 21:10:41,574 middleware 18476 18448 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 51.68, "response_size": 421, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:10:41,575 basehttp 18476 18448 "POST /api/documents/upload/ HTTP/1.1" 201 421
INFO 2025-06-28 21:14:22,621 autoreload 17848 13964 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:14:22,679 autoreload 9348 5444 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:14:22,783 autoreload 18476 15616 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:14:25,494 autoreload 8736 13476 Watching for file changes with StatReloader
INFO 2025-06-28 21:14:25,502 autoreload 10396 19276 Watching for file changes with StatReloader
INFO 2025-06-28 21:14:25,574 autoreload 5536 18112 Watching for file changes with StatReloader
INFO 2025-06-28 21:14:33,663 middleware 10396 17120 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:14:33,741 middleware 10396 17120 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 78.41, "response_size": 2308, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:14:33,743 basehttp 10396 17120 "GET /api/documents/ HTTP/1.1" 200 2308
INFO 2025-06-28 21:14:33,762 middleware 10396 13732 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "1197", "upload_size": 910}
INFO 2025-06-28 21:14:33,822 middleware 10396 13732 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 59.62, "response_size": 429, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:14:33,824 basehttp 10396 13732 "POST /api/documents/upload/ HTTP/1.1" 201 429
INFO 2025-06-28 21:14:53,067 middleware 10396 15236 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:14:53,109 middleware 10396 15236 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 41.14, "response_size": 2738, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:14:53,110 basehttp 10396 15236 "GET /api/documents/ HTTP/1.1" 200 2738
INFO 2025-06-28 21:14:53,127 middleware 10396 20856 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "1197", "upload_size": 910}
INFO 2025-06-28 21:14:53,179 middleware 10396 20856 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 52.41, "response_size": 429, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:14:53,180 basehttp 10396 20856 "POST /api/documents/upload/ HTTP/1.1" 201 429
INFO 2025-06-28 21:15:15,368 autoreload 10396 19276 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:15:15,475 autoreload 8736 13476 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:15:15,585 autoreload 5536 18112 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py changed, reloading.
INFO 2025-06-28 21:15:18,699 autoreload 1320 3204 Watching for file changes with StatReloader
INFO 2025-06-28 21:15:18,726 autoreload 13468 19128 Watching for file changes with StatReloader
INFO 2025-06-28 21:15:18,792 autoreload 4376 19748 Watching for file changes with StatReloader
INFO 2025-06-28 21:18:16,470 middleware 1320 18456 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:18:16,473 middleware 1320 19796 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:18:16,474 middleware 1320 17340 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:18:16,482 middleware 1320 2512 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:18:16,483 middleware 1320 18456 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5D9960>.", "duration_ms": 12.6, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:18:16,484 middleware 1320 19796 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5DA380>.", "duration_ms": 11.01, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:18:16,484 middleware 1320 17340 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5DAA10>.", "duration_ms": 10.01, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:18:16,487 middleware 1320 2512 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5DB670>.", "duration_ms": 5.0, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:18:16,502 middleware 1320 21420 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:18:16,517 middleware 1320 7316 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:18:16,650 middleware 1320 21420 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 148.09, "response_size": 3168, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:18:16,652 middleware 1320 7316 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 134.19, "response_size": 3168, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:18:16,654 basehttp 1320 21420 "GET /api/documents/ HTTP/1.1" 200 3168
INFO 2025-06-28 21:18:16,655 basehttp 1320 7316 "GET /api/documents/ HTTP/1.1" 200 3168
ERROR 2025-06-28 21:18:16,752 log 1320 2512 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 45, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5DB670>.
ERROR 2025-06-28 21:18:16,781 log 1320 19796 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 45, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5DA380>.
ERROR 2025-06-28 21:18:16,797 log 1320 17340 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 45, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5DAA10>.
ERROR 2025-06-28 21:18:16,817 log 1320 18456 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 45, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5D9960>.
ERROR 2025-06-28 21:18:16,832 middleware 1320 2512 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 350.59, "response_size": 171633, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:18:16,834 middleware 1320 19796 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 361.6, "response_size": 171633, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:18:16,840 basehttp 1320 2512 "GET /api/courses/ HTTP/1.1" 500 171633
ERROR 2025-06-28 21:18:16,842 basehttp 1320 19796 "GET /api/courses/ HTTP/1.1" 500 171633
ERROR 2025-06-28 21:18:16,843 middleware 1320 17340 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 369.68, "response_size": 171633, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:18:16,847 middleware 1320 18456 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 377.26, "response_size": 171633, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:18:16,852 basehttp 1320 17340 "GET /api/courses/ HTTP/1.1" 500 171633
ERROR 2025-06-28 21:18:16,855 basehttp 1320 18456 "GET /api/courses/ HTTP/1.1" 500 171633
INFO 2025-06-28 21:18:22,585 middleware 1320 9276 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}
WARNING 2025-06-28 21:18:22,588 middleware 1320 9276 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 4.01, "response_size": 37, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 21:18:22,588 log 1320 9276 Bad Request: /api/documents/upload/
WARNING 2025-06-28 21:18:22,591 basehttp 1320 9276 "POST /api/documents/upload/ HTTP/1.1" 400 37
INFO 2025-06-28 21:34:02,734 middleware 1320 14044 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.31.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:34:02,786 middleware 1320 14044 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 53.61, "response_size": 3168, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:34:02,788 basehttp 1320 14044 "GET /api/documents/ HTTP/1.1" 200 3168
INFO 2025-06-28 21:34:02,812 middleware 1320 21396 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "1197", "upload_size": 910}
INFO 2025-06-28 21:34:02,852 middleware 1320 21396 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 40.78, "response_size": 429, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:34:02,853 basehttp 1320 21396 "POST /api/documents/upload/ HTTP/1.1" 201 429
INFO 2025-06-28 21:34:32,003 autoreload 13468 19128 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\serializers.py changed, reloading.
INFO 2025-06-28 21:34:32,234 autoreload 1320 3204 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\serializers.py changed, reloading.
INFO 2025-06-28 21:34:32,241 autoreload 4376 19748 C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\serializers.py changed, reloading.
INFO 2025-06-28 21:34:33,573 autoreload 19424 5260 Watching for file changes with StatReloader
INFO 2025-06-28 21:34:33,624 autoreload 19048 16888 Watching for file changes with StatReloader
INFO 2025-06-28 21:34:33,635 autoreload 12268 20464 Watching for file changes with StatReloader
INFO 2025-06-28 21:34:51,544 middleware 19048 14760 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.31.0", "ip_address": "127.0.0.1", "content_length": "223", "upload_size": 44}
WARNING 2025-06-28 21:34:51,552 middleware 19048 14760 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 8.14, "response_size": 77, "ip_address": "127.0.0.1"}
WARNING 2025-06-28 21:34:51,553 log 19048 14760 Bad Request: /api/documents/upload/
WARNING 2025-06-28 21:34:51,554 basehttp 19048 14760 "POST /api/documents/upload/ HTTP/1.1" 400 77
INFO 2025-06-28 21:35:02,474 middleware 19048 10396 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.31.0", "ip_address": "127.0.0.1", "content_length": "629", "upload_size": 450}
INFO 2025-06-28 21:35:02,536 middleware 19048 10396 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 62.08, "response_size": 412, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:35:02,537 basehttp 19048 10396 "POST /api/documents/upload/ HTTP/1.1" 201 412
INFO 2025-06-28 21:35:09,460 middleware 19048 14124 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.31.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:35:09,509 middleware 19048 14124 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 48.7, "response_size": 4011, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:35:09,510 basehttp 19048 14124 "GET /api/documents/ HTTP/1.1" 200 4011
INFO 2025-06-28 21:35:09,524 middleware 19048 21108 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "1197", "upload_size": 910}
INFO 2025-06-28 21:35:09,565 middleware 19048 21108 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 40.93, "response_size": 420, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:35:09,566 basehttp 19048 21108 "POST /api/documents/upload/ HTTP/1.1" 201 420
INFO 2025-06-28 21:35:31,004 middleware 19048 20492 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:35:31,005 middleware 19048 21184 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:35:31,007 middleware 19048 18956 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:35:31,008 middleware 19048 19060 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:35:31,010 middleware 19048 20492 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38EE0280>.", "duration_ms": 5.71, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:35:31,011 middleware 19048 20532 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:35:31,012 middleware 19048 21184 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38E1B790>.", "duration_ms": 6.72, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:35:31,013 middleware 19048 19108 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:35:31,015 middleware 19048 18956 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38E18AF0>.", "duration_ms": 8.72, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:31,016 middleware 19048 19060 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38E1A440>.", "duration_ms": 7.61, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:35:31,102 middleware 19048 20532 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 91.37, "response_size": 4432, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:35:31,108 basehttp 19048 20532 "GET /api/documents/ HTTP/1.1" 200 4432
INFO 2025-06-28 21:35:31,135 middleware 19048 19108 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 122.63, "response_size": 4432, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:35:31,142 basehttp 19048 19108 "GET /api/documents/ HTTP/1.1" 200 4432
ERROR 2025-06-28 21:35:31,250 log 19048 20492 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 45, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38EE0280>.
ERROR 2025-06-28 21:35:31,269 log 19048 21184 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 45, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38E1B790>.
ERROR 2025-06-28 21:35:31,305 log 19048 19060 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 45, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38E1A440>.
ERROR 2025-06-28 21:35:31,310 log 19048 18956 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 45, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38E18AF0>.
ERROR 2025-06-28 21:35:31,316 middleware 19048 20492 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 311.63, "response_size": 170877, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:31,319 middleware 19048 21184 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 314.53, "response_size": 170877, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:31,321 middleware 19048 19060 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 313.1, "response_size": 170877, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:31,324 basehttp 19048 20492 "GET /api/courses/ HTTP/1.1" 500 170877
ERROR 2025-06-28 21:35:31,324 middleware 19048 18956 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 318.23, "response_size": 170877, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:31,326 basehttp 19048 21184 "GET /api/courses/ HTTP/1.1" 500 170877
ERROR 2025-06-28 21:35:31,326 basehttp 19048 19060 "GET /api/courses/ HTTP/1.1" 500 170877
ERROR 2025-06-28 21:35:31,330 basehttp 19048 18956 "GET /api/courses/ HTTP/1.1" 500 170877
INFO 2025-06-28 21:35:38,193 middleware 19048 20444 {"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}
INFO 2025-06-28 21:35:38,247 middleware 19048 20444 {"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 54.46, "response_size": 699, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:35:38,248 basehttp 19048 20444 "POST /api/documents/upload/ HTTP/1.1" 201 699
INFO 2025-06-28 21:35:38,262 middleware 19048 20604 {"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
INFO 2025-06-28 21:35:38,263 middleware 19048 18904 {"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:35:38,264 middleware 19048 20604 {"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C58F40>.", "duration_ms": 2.69, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:35:38,320 middleware 19048 18904 {"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 57.41, "response_size": 5132, "ip_address": "127.0.0.1"}
INFO 2025-06-28 21:35:38,321 basehttp 19048 18904 "GET /api/documents/ HTTP/1.1" 200 5132
ERROR 2025-06-28 21:35:38,372 log 19048 20604 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 45, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C58F40>.
ERROR 2025-06-28 21:35:38,377 middleware 19048 20604 {"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 115.44, "response_size": 170877, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:38,379 basehttp 19048 20604 "GET /api/courses/ HTTP/1.1" 500 170877
INFO 2025-06-28 21:35:41,274 middleware 19048 18460 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:35:41,278 middleware 19048 18460 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC7DC0>.", "duration_ms": 5.02, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:41,533 log 19048 18460 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC7DC0>.
ERROR 2025-06-28 21:35:41,536 middleware 19048 18460 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 262.42, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:41,537 basehttp 19048 18460 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:35:44,578 middleware 19048 17264 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:35:44,580 middleware 19048 17264 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2F0D0>.", "duration_ms": 2.12, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:44,763 log 19048 17264 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2F0D0>.
ERROR 2025-06-28 21:35:44,766 middleware 19048 17264 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 187.49, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:44,768 basehttp 19048 17264 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:35:47,628 middleware 19048 20056 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:35:47,630 middleware 19048 20056 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC51E0>.", "duration_ms": 2.02, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:47,811 log 19048 20056 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC51E0>.
ERROR 2025-06-28 21:35:47,814 middleware 19048 20056 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 186.44, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:47,816 basehttp 19048 20056 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:35:50,982 middleware 19048 19176 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:35:50,985 middleware 19048 19176 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F72A10>.", "duration_ms": 2.53, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:51,163 log 19048 19176 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F72A10>.
ERROR 2025-06-28 21:35:51,167 middleware 19048 19176 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 185.17, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:51,168 basehttp 19048 19176 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:35:53,979 middleware 19048 20296 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:35:53,981 middleware 19048 20296 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2CF70>.", "duration_ms": 1.02, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:54,171 log 19048 20296 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2CF70>.
ERROR 2025-06-28 21:35:54,175 middleware 19048 20296 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 195.13, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:54,176 basehttp 19048 20296 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:35:57,210 middleware 19048 20496 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:35:57,210 middleware 19048 20496 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F72800>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:57,399 log 19048 20496 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F72800>.
ERROR 2025-06-28 21:35:57,403 middleware 19048 20496 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 193.25, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:35:57,408 basehttp 19048 20496 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:36:00,221 middleware 19048 16556 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:36:00,223 middleware 19048 16556 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2FF70>.", "duration_ms": 1.99, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:00,381 log 19048 16556 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2FF70>.
ERROR 2025-06-28 21:36:00,385 middleware 19048 16556 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 164.55, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:00,386 basehttp 19048 16556 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:36:03,253 middleware 19048 15540 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:36:03,256 middleware 19048 15540 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F72AA0>.", "duration_ms": 3.68, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:03,444 log 19048 15540 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F72AA0>.
ERROR 2025-06-28 21:36:03,448 middleware 19048 15540 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 195.2, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:03,448 basehttp 19048 15540 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:36:06,234 middleware 19048 12668 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:36:06,235 middleware 19048 12668 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C5A980>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:06,419 log 19048 12668 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C5A980>.
ERROR 2025-06-28 21:36:06,419 middleware 19048 12668 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 184.88, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:06,428 basehttp 19048 12668 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:36:09,239 middleware 19048 5188 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:36:09,240 middleware 19048 5188 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C5A260>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:09,470 log 19048 5188 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C5A260>.
ERROR 2025-06-28 21:36:09,476 middleware 19048 5188 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 236.59, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:09,477 basehttp 19048 5188 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:36:12,244 middleware 19048 10460 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:36:12,246 middleware 19048 10460 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38ECE950>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:12,526 log 19048 10460 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38ECE950>.
ERROR 2025-06-28 21:36:12,531 middleware 19048 10460 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 287.6, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:12,534 basehttp 19048 10460 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:36:15,271 middleware 19048 20964 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:36:15,271 middleware 19048 20964 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2F790>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:15,553 log 19048 20964 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2F790>.
ERROR 2025-06-28 21:36:15,559 middleware 19048 20964 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 287.93, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:15,560 basehttp 19048 20964 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:36:18,464 middleware 19048 7892 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:36:18,466 middleware 19048 7892 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38ECE0B0>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:18,639 log 19048 7892 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38ECE0B0>.
ERROR 2025-06-28 21:36:18,644 middleware 19048 7892 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 179.87, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:18,646 basehttp 19048 7892 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:36:21,478 middleware 19048 21120 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:36:21,480 middleware 19048 21120 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC42E0>.", "duration_ms": 1.55, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:21,742 log 19048 21120 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC42E0>.
ERROR 2025-06-28 21:36:21,747 middleware 19048 21120 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 269.11, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:21,749 basehttp 19048 21120 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:36:24,486 middleware 19048 2296 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:36:24,488 middleware 19048 2296 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C586D0>.", "duration_ms": 1.58, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:24,687 log 19048 2296 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C586D0>.
ERROR 2025-06-28 21:36:24,692 middleware 19048 2296 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 205.58, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:24,693 basehttp 19048 2296 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:36:27,489 middleware 19048 12996 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:36:27,489 middleware 19048 12996 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39093100>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:27,663 log 19048 12996 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39093100>.
ERROR 2025-06-28 21:36:27,668 middleware 19048 12996 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 178.79, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:27,672 basehttp 19048 12996 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:36:30,491 middleware 19048 5888 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:36:30,495 middleware 19048 5888 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38EB9C90>.", "duration_ms": 4.11, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:30,681 log 19048 5888 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38EB9C90>.
ERROR 2025-06-28 21:36:30,687 middleware 19048 5888 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 196.53, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:30,688 basehttp 19048 5888 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:36:33,500 middleware 19048 2740 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:36:33,502 middleware 19048 2740 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FDDDB0>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:33,685 log 19048 2740 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FDDDB0>.
ERROR 2025-06-28 21:36:33,688 middleware 19048 2740 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 188.61, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:33,690 basehttp 19048 2740 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:36:36,508 middleware 19048 14680 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:36:36,509 middleware 19048 14680 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2F820>.", "duration_ms": 1.18, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:36,682 log 19048 14680 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2F820>.
ERROR 2025-06-28 21:36:36,689 middleware 19048 14680 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 181.04, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:36,689 basehttp 19048 14680 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:36:39,516 middleware 19048 7528 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:36:39,518 middleware 19048 7528 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FDE110>.", "duration_ms": 2.03, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:39,702 log 19048 7528 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FDE110>.
ERROR 2025-06-28 21:36:39,705 middleware 19048 7528 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 188.71, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:39,706 basehttp 19048 7528 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:36:42,530 middleware 19048 11860 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:36:42,530 middleware 19048 11860 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39092F80>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:42,724 log 19048 11860 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39092F80>.
ERROR 2025-06-28 21:36:42,729 middleware 19048 11860 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 199.34, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:42,730 basehttp 19048 11860 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
INFO 2025-06-28 21:36:45,545 middleware 19048 12148 {"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}
ERROR 2025-06-28 21:36:45,546 middleware 19048 12148 {"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2C3D0>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:45,773 log 19048 12148 Internal Server Error: /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 286, in get
    return self.retrieve(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 119, in retrieve
    instance = self.get_object()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 87, in get_object
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 116, in get_queryset
    return Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2C3D0>.
ERROR 2025-06-28 21:36:45,776 middleware 19048 12148 {"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 230.73, "response_size": 178421, "ip_address": "127.0.0.1"}
ERROR 2025-06-28 21:36:45,778 basehttp 19048 12148 "GET /api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/ HTTP/1.1" 500 178421
