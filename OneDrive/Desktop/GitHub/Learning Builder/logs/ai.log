{"level": "INFO", "time": "2025-06-28 19:24:23,261", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2047", "upload_size": 1806}"}
{"level": "INFO", "time": "2025-06-28 19:24:23,355", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 94.26, "response_size": 62, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:23,376", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:24:23,395", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:24:23,426", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "status_code": 200, "duration_ms": 50.32, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:23,438", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "status_code": 200, "duration_ms": 42.83, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:26,618", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "ERROR", "time": "2025-06-28 19:24:26,678", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/", "status_code": 500, "duration_ms": 60.09, "response_size": 92, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:28,742", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:24:28,762", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:24:28,772", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "status_code": 200, "duration_ms": 30.08, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:28,811", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "status_code": 200, "duration_ms": 49.32, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:29,748", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "ERROR", "time": "2025-06-28 19:24:29,799", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/", "status_code": 500, "duration_ms": 52.27, "response_size": 74, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:44,665", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "INFO", "time": "2025-06-28 19:24:44,721", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 57.42, "response_size": 62, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:44,737", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:24:44,775", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "status_code": 200, "duration_ms": 37.99, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:47,554", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "ERROR", "time": "2025-06-28 19:24:47,704", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "status_code": 500, "duration_ms": 150.63, "response_size": 92, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:49,062", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "ERROR", "time": "2025-06-28 19:24:49,259", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "status_code": 500, "duration_ms": 197.0, "response_size": 92, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:27:49,542", "module": "tasks", "message": "Successfully parsed 2 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,542", "module": "tasks", "message": "Parsing AI flashcards output: {"front": "API", "back": "Application Programming Interface"}..."}
{"level": "WARNING", "time": "2025-06-28 19:27:49,543", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,544", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,544", "module": "tasks", "message": "Parsing AI flashcards output: Random text without proper format..."}
{"level": "WARNING", "time": "2025-06-28 19:27:49,545", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,545", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,546", "module": "tasks", "message": "Parsing AI flashcards output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:27:49,546", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,547", "module": "tasks", "message": "Successfully parsed 0 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,550", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:27:49,551", "module": "tasks", "message": "Parsing AI questions output: Random question text..."}
{"level": "WARNING", "time": "2025-06-28 19:27:49,552", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:27:49,552", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:27:49,552", "module": "tasks", "message": "Parsing AI questions output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:27:49,552", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:27:49,553", "module": "tasks", "message": "Successfully parsed 0 questions"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,952", "module": "client", "message": "Failed to initialize OpenAI client: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,953", "module": "client", "message": "Fallback also failed: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "INFO", "time": "2025-06-28 19:29:00,957", "module": "tasks", "message": "Successfully parsed 2 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,958", "module": "tasks", "message": "Parsing AI flashcards output: {"front": "API", "back": "Application Programming Interface"}..."}
{"level": "WARNING", "time": "2025-06-28 19:29:00,958", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,959", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,959", "module": "tasks", "message": "Parsing AI flashcards output: Random text without proper format..."}
{"level": "WARNING", "time": "2025-06-28 19:29:00,959", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,960", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,960", "module": "tasks", "message": "Parsing AI flashcards output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:29:00,960", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,961", "module": "tasks", "message": "Successfully parsed 0 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,963", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:29:00,964", "module": "tasks", "message": "Parsing AI questions output: Random question text..."}
{"level": "WARNING", "time": "2025-06-28 19:29:00,965", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:29:00,965", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:29:00,966", "module": "tasks", "message": "Parsing AI questions output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:29:00,966", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:29:00,967", "module": "tasks", "message": "Successfully parsed 0 questions"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,967", "module": "client", "message": "Failed to initialize OpenAI client: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,968", "module": "client", "message": "Fallback also failed: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,972", "module": "client", "message": "Failed to initialize OpenAI client: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,973", "module": "client", "message": "Fallback also failed: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "INFO", "time": "2025-06-28 19:29:22,649", "module": "client", "message": "Making OpenAI API call for unknown"}
{"level": "INFO", "time": "2025-06-28 19:29:25,086", "module": "client", "message": "Successfully generated content with 12 characters"}
{"level": "INFO", "time": "2025-06-28 19:29:25,093", "module": "tasks", "message": "Successfully parsed 2 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,093", "module": "tasks", "message": "Parsing AI flashcards output: {"front": "API", "back": "Application Programming Interface"}..."}
{"level": "WARNING", "time": "2025-06-28 19:29:25,094", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,094", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,095", "module": "tasks", "message": "Parsing AI flashcards output: Random text without proper format..."}
{"level": "WARNING", "time": "2025-06-28 19:29:25,095", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,096", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,096", "module": "tasks", "message": "Parsing AI flashcards output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:29:25,097", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,097", "module": "tasks", "message": "Successfully parsed 0 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,100", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:29:25,100", "module": "tasks", "message": "Parsing AI questions output: Random question text..."}
{"level": "WARNING", "time": "2025-06-28 19:29:25,101", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:29:25,103", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:29:25,104", "module": "tasks", "message": "Parsing AI questions output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:29:25,105", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:29:25,105", "module": "tasks", "message": "Successfully parsed 0 questions"}
{"level": "INFO", "time": "2025-06-28 19:29:25,118", "module": "client", "message": "Making OpenAI API call for flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:30,431", "module": "client", "message": "Successfully generated flashcards with 1407 characters"}
{"level": "INFO", "time": "2025-06-28 19:29:30,434", "module": "tasks", "message": "Successfully parsed 10 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:30,447", "module": "client", "message": "Making OpenAI API call for questions"}
{"level": "INFO", "time": "2025-06-28 19:29:45,734", "module": "client", "message": "Successfully generated questions with 3273 characters"}
{"level": "INFO", "time": "2025-06-28 19:29:45,740", "module": "tasks", "message": "Successfully parsed 19 questions"}
{"level": "INFO", "time": "2025-06-28 19:30:04,229", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "INFO", "time": "2025-06-28 19:30:04,415", "module": "cache", "message": "Cache miss for flashcards with key ai_result:flashcards:bc67034f5423dadf"}
{"level": "INFO", "time": "2025-06-28 19:30:04,417", "module": "client", "message": "Making OpenAI API call for flashcards"}
{"level": "INFO", "time": "2025-06-28 19:30:10,205", "module": "cache", "message": "Cached flashcards result with key ai_result:flashcards:bc67034f5423dadf for 604800 seconds"}
{"level": "INFO", "time": "2025-06-28 19:30:10,205", "module": "client", "message": "Successfully generated flashcards with 1918 characters"}
{"level": "INFO", "time": "2025-06-28 19:30:10,406", "module": "tasks", "message": "Successfully parsed 10 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:30:10,464", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "status_code": 200, "duration_ms": 6234.64, "response_size": 15, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:30:10,480", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:30:10,522", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "status_code": 200, "duration_ms": 42.12, "response_size": 5124, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:30:31,351", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:30:31,352", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:30:31,382", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "status_code": 200, "duration_ms": 30.21, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:30:31,397", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "status_code": 200, "duration_ms": 45.01, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:30:32,411", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "INFO", "time": "2025-06-28 19:30:32,601", "module": "cache", "message": "Cache miss for questions with key ai_result:questions:abb5dfd3fb19bd4c"}
{"level": "INFO", "time": "2025-06-28 19:30:32,601", "module": "client", "message": "Making OpenAI API call for questions"}
{"level": "INFO", "time": "2025-06-28 19:30:48,883", "module": "cache", "message": "Cached questions result with key ai_result:questions:abb5dfd3fb19bd4c for 604800 seconds"}
{"level": "INFO", "time": "2025-06-28 19:30:48,884", "module": "client", "message": "Successfully generated questions with 4448 characters"}
{"level": "INFO", "time": "2025-06-28 19:30:48,902", "module": "tasks", "message": "Successfully parsed 20 questions"}
{"level": "INFO", "time": "2025-06-28 19:30:48,989", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/questions/", "status_code": 200, "duration_ms": 16577.25, "response_size": 15, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:30:49,003", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:30:49,032", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "status_code": 200, "duration_ms": 29.33, "response_size": 10638, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:33:22,466", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/pdf/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:33:22,576", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/pdf/", "status_code": 200, "duration_ms": 111.24, "response_size": 0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:33:27,227", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/csv/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:33:27,263", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/csv/", "status_code": 200, "duration_ms": 35.83, "response_size": 0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:33:52,320", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/anki/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:33:52,455", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/anki/", "status_code": 200, "duration_ms": 134.81, "response_size": 0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:25:55,317", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/docs/", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; bg-BG) WindowsPowerShell/5.1.26100.4484", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:25:55,333", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/docs/", "status_code": 200, "duration_ms": 15.99, "response_size": 4640, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:31:46,969", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 20:31:46,998", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 31.04, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:32:19,958", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 20:32:19,964", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 5.69, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:32:29,652", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2051", "upload_size": 1806}"}
{"level": "WARNING", "time": "2025-06-28 20:32:29,657", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 6.53, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:32:31,611", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2051", "upload_size": 1806}"}
{"level": "WARNING", "time": "2025-06-28 20:32:31,618", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 7.0, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:38:32,827", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 20:38:32,828", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295565BAAA0>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:38:32,851", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 20:38:32,853", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295565BB5B0>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:38:33,059", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 206.94, "response_size": 170572, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:38:33,062", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 233.94, "response_size": 170572, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:39:17,035", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 20:39:17,037", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295564D9420>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:39:17,037", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:39:17,039", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 20:39:17,047", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000029556433D30>.", "duration_ms": 9.53, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:39:17,049", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000029556701390>.", "duration_ms": 10.03, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:39:17,187", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 149.84, "response_size": 170572, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:39:17,191", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 155.88, "response_size": 170572, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:39:17,227", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 500, "duration_ms": 187.94, "response_size": 170527, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:42:24,144", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 20:42:24,152", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 8.55, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:46:57,720", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:46:57,721", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:46:57,721", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:46:57,721", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:46:57,722", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:46:57,724", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 20:46:57,733", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E285E0>.", "duration_ms": 12.99, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:57,733", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E2BD00>.", "duration_ms": 12.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:57,734", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E646D0>.", "duration_ms": 12.99, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:57,735", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E65000>.", "duration_ms": 14.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:57,735", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E65A50>.", "duration_ms": 13.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:57,736", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E66380>.", "duration_ms": 12.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:58,105", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 385.13, "response_size": 171328, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:58,108", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 386.15, "response_size": 171328, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:58,116", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 394.14, "response_size": 171328, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:58,120", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 500, "duration_ms": 399.15, "response_size": 171283, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:58,123", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 500, "duration_ms": 399.14, "response_size": 171283, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:58,125", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 404.14, "response_size": 171328, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:47:05,992", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 20:47:06,018", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 26.02, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:51:29,220", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:51:29,285", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 64.93, "response_size": 606, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:51:29,311", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "350", "upload_size": 74}"}
{"level": "WARNING", "time": "2025-06-28 20:51:29,342", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 32.2, "response_size": 77, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:51:53,280", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:51:53,317", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 36.42, "response_size": 606, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:51:53,337", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "779", "upload_size": 503}"}
{"level": "INFO", "time": "2025-06-28 20:51:53,403", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 66.78, "response_size": 410, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:51:53,412", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:51:53,461", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 48.97, "response_size": 1017, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:59:26,646", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 20:59:26,675", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 28.99, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:02:41,179", "module": "middleware", "message": "{"event": "request_start", "method": "OPTIONS", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0"}"}
{"level": "INFO", "time": "2025-06-28 21:02:41,180", "module": "middleware", "message": "{"event": "request_complete", "method": "OPTIONS", "path": "/api/documents/upload/", "status_code": 200, "duration_ms": 0.99, "response_size": 0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:02:41,204", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "1202", "upload_size": 905}"}
{"level": "INFO", "time": "2025-06-28 21:02:41,296", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 92.02, "response_size": 431, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:02:41,308", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/flashcards/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "ERROR", "time": "2025-06-28 21:02:42,269", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/flashcards/", "status_code": 500, "duration_ms": 961.22, "response_size": 225, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:02:42,278", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/quiz/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "ERROR", "time": "2025-06-28 21:02:42,286", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/quiz/", "status_code": 500, "duration_ms": 8.18, "response_size": 215, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:02:42,299", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/study-sets/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:02:42,305", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/study-sets/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B864909BD0>.", "duration_ms": 6.72, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:02:42,462", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/study-sets/", "status_code": 500, "duration_ms": 162.94, "response_size": 170417, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:04:32,797", "module": "middleware", "message": "{"event": "request_start", "method": "OPTIONS", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0"}"}
{"level": "INFO", "time": "2025-06-28 21:04:32,798", "module": "middleware", "message": "{"event": "request_complete", "method": "OPTIONS", "path": "/api/documents/upload/", "status_code": 200, "duration_ms": 1.0, "response_size": 0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:04:32,808", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "1202", "upload_size": 905}"}
{"level": "WARNING", "time": "2025-06-28 21:04:32,842", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 403, "duration_ms": 34.16, "response_size": 63, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:05:26,219", "module": "middleware", "message": "{"event": "request_start", "method": "OPTIONS", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0"}"}
{"level": "INFO", "time": "2025-06-28 21:05:26,221", "module": "middleware", "message": "{"event": "request_complete", "method": "OPTIONS", "path": "/api/documents/upload/", "status_code": 200, "duration_ms": 1.99, "response_size": 0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:05:26,232", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "1202", "upload_size": 905}"}
{"level": "WARNING", "time": "2025-06-28 21:05:26,270", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 403, "duration_ms": 38.11, "response_size": 63, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:05:48,927", "module": "middleware", "message": "{"event": "request_start", "method": "OPTIONS", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0"}"}
{"level": "INFO", "time": "2025-06-28 21:05:48,928", "module": "middleware", "message": "{"event": "request_complete", "method": "OPTIONS", "path": "/api/documents/upload/", "status_code": 200, "duration_ms": 2.02, "response_size": 0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:05:48,946", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "1202", "upload_size": 905}"}
{"level": "INFO", "time": "2025-06-28 21:05:49,022", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 76.97, "response_size": 439, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:05:49,031", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/962c7fb0-53ff-4efa-b8e1-f84e40b967ef/generate/flashcards/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "INFO", "time": "2025-06-28 21:05:50,193", "module": "cache", "message": "Cache miss for flashcards with key ai_result:flashcards:a31c2dcc858f8cff"}
{"level": "INFO", "time": "2025-06-28 21:05:50,196", "module": "client", "message": "Making OpenAI API call for flashcards"}
{"level": "INFO", "time": "2025-06-28 21:05:58,782", "module": "cache", "message": "Cached flashcards result with key ai_result:flashcards:a31c2dcc858f8cff for 604800 seconds"}
{"level": "INFO", "time": "2025-06-28 21:05:58,782", "module": "client", "message": "Successfully generated flashcards with 1381 characters"}
{"level": "INFO", "time": "2025-06-28 21:05:58,808", "module": "tasks", "message": "Successfully parsed 10 flashcards"}
{"level": "INFO", "time": "2025-06-28 21:05:58,882", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/962c7fb0-53ff-4efa-b8e1-f84e40b967ef/generate/flashcards/", "status_code": 200, "duration_ms": 9850.98, "response_size": 96, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:05:58,892", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/962c7fb0-53ff-4efa-b8e1-f84e40b967ef/generate/quiz/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "INFO", "time": "2025-06-28 21:05:58,959", "module": "cache", "message": "Cache miss for questions with key ai_result:questions:1ca776486586604a"}
{"level": "INFO", "time": "2025-06-28 21:05:58,961", "module": "client", "message": "Making OpenAI API call for questions"}
{"level": "INFO", "time": "2025-06-28 21:06:10,329", "module": "cache", "message": "Cached questions result with key ai_result:questions:1ca776486586604a for 604800 seconds"}
{"level": "INFO", "time": "2025-06-28 21:06:10,330", "module": "client", "message": "Successfully generated questions with 4044 characters"}
{"level": "INFO", "time": "2025-06-28 21:06:10,339", "module": "tasks", "message": "Successfully parsed 18 questions"}
{"level": "INFO", "time": "2025-06-28 21:06:10,440", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/962c7fb0-53ff-4efa-b8e1-f84e40b967ef/generate/quiz/", "status_code": 200, "duration_ms": 11548.0, "response_size": 144, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:06:10,446", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/study-sets/b147fce2-8bfe-44bc-be25-f5bc13d59599/quiz/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:06:10,523", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/study-sets/b147fce2-8bfe-44bc-be25-f5bc13d59599/quiz/", "status_code": 200, "duration_ms": 76.14, "response_size": 10160, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:06:10,529", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/study-sets/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:06:10,595", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/study-sets/", "status_code": 200, "duration_ms": 66.31, "response_size": 3704, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:06:10,601", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/study-sets/49d1142b-fb03-4d25-8e32-941045f78774/flashcards/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:06:10,643", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/study-sets/49d1142b-fb03-4d25-8e32-941045f78774/flashcards/", "status_code": 200, "duration_ms": 42.28, "response_size": 5727, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:07:26,010", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 21:07:26,013", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 4.01, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:07:28,546", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:07:28,548", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:07:28,549", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:07:28,551", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:07:28,553", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D135DB0>.", "duration_ms": 7.0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:07:28,554", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:07:28,554", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D313B50>.", "duration_ms": 7.08, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:07:28,556", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D2CE170>.", "duration_ms": 8.1, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:07:28,557", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D4365C0>.", "duration_ms": 6.11, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:07:28,570", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:07:28,613", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 59.13, "response_size": 1886, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:07:28,647", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 76.87, "response_size": 1886, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:07:28,782", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 231.77, "response_size": 171420, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:07:28,787", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 239.77, "response_size": 171420, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:07:28,791", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 245.79, "response_size": 171420, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:07:28,794", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 246.78, "response_size": 171420, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:07:34,614", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2051", "upload_size": 1806}"}
{"level": "WARNING", "time": "2025-06-28 21:07:34,617", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 3.97, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:07:35,830", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2049", "upload_size": 1806}"}
{"level": "WARNING", "time": "2025-06-28 21:07:35,832", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 3.0, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:07:46,898", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2051", "upload_size": 1806}"}
{"level": "WARNING", "time": "2025-06-28 21:07:46,900", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 3.02, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:08:49,275", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:08:49,277", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:08:49,278", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2B7BD210>.", "duration_ms": 3.0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:08:49,304", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:08:49,305", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:08:49,306", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:08:49,307", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:08:49,309", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2DD4DBA0>.", "duration_ms": 4.63, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:08:49,311", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2DD4CE80>.", "duration_ms": 5.78, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:08:49,312", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2DD4EC20>.", "duration_ms": 6.38, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:08:49,320", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 43.78, "response_size": 1886, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:08:49,363", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 55.71, "response_size": 1886, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:08:49,413", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 137.83, "response_size": 171420, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:08:49,564", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 258.66, "response_size": 171420, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:08:49,569", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 264.7, "response_size": 171420, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:08:49,574", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 270.25, "response_size": 171420, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:09:18,941", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 21:09:18,948", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 8.01, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:09:56,942", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; bg-BG) WindowsPowerShell/5.1.26100.4484", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:09:57,000", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 58.27, "response_size": 1886, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:12,207", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:10:12,208", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:10:12,211", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E45F6C670>.", "duration_ms": 3.66, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:12,212", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:10:12,215", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E45F6C8B0>.", "duration_ms": 6.36, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:12,215", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:10:12,217", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:10:12,291", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E46029510>.", "duration_ms": 78.79, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:12,295", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:10:12,305", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E45EE2F20>.", "duration_ms": 89.56, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:12,357", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 139.88, "response_size": 1886, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:12,371", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 76.07, "response_size": 1886, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:10:12,592", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 384.83, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:10:12,595", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 380.07, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:10:12,599", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 386.59, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:10:12,602", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 393.33, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:26,073", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 21:10:26,076", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 4.06, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:41,446", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:10:41,487", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 40.77, "response_size": 1886, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:41,523", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "1197", "upload_size": 910}"}
{"level": "INFO", "time": "2025-06-28 21:10:41,574", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 51.68, "response_size": 421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:14:33,663", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:14:33,741", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 78.41, "response_size": 2308, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:14:33,762", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "1197", "upload_size": 910}"}
{"level": "INFO", "time": "2025-06-28 21:14:33,822", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 59.62, "response_size": 429, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:14:53,067", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:14:53,109", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 41.14, "response_size": 2738, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:14:53,127", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "1197", "upload_size": 910}"}
{"level": "INFO", "time": "2025-06-28 21:14:53,179", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 52.41, "response_size": 429, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:18:16,470", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:18:16,473", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:18:16,474", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:18:16,482", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:18:16,483", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5D9960>.", "duration_ms": 12.6, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:18:16,484", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5DA380>.", "duration_ms": 11.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:18:16,484", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5DAA10>.", "duration_ms": 10.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:18:16,487", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5DB670>.", "duration_ms": 5.0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:18:16,502", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:18:16,517", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:18:16,650", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 148.09, "response_size": 3168, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:18:16,652", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 134.19, "response_size": 3168, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:18:16,832", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 350.59, "response_size": 171633, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:18:16,834", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 361.6, "response_size": 171633, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:18:16,843", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 369.68, "response_size": 171633, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:18:16,847", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 377.26, "response_size": 171633, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:18:22,585", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 21:18:22,588", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 4.01, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:34:02,734", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.31.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:34:02,786", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 53.61, "response_size": 3168, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:34:02,812", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "1197", "upload_size": 910}"}
{"level": "INFO", "time": "2025-06-28 21:34:02,852", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 40.78, "response_size": 429, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:34:51,544", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.31.0", "ip_address": "127.0.0.1", "content_length": "223", "upload_size": 44}"}
{"level": "WARNING", "time": "2025-06-28 21:34:51,552", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 8.14, "response_size": 77, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:02,474", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.31.0", "ip_address": "127.0.0.1", "content_length": "629", "upload_size": 450}"}
{"level": "INFO", "time": "2025-06-28 21:35:02,536", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 62.08, "response_size": 412, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:09,460", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.31.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:35:09,509", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 48.7, "response_size": 4011, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:09,524", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "1197", "upload_size": 910}"}
{"level": "INFO", "time": "2025-06-28 21:35:09,565", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 40.93, "response_size": 420, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:31,004", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:35:31,005", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:35:31,007", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:35:31,008", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:31,010", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38EE0280>.", "duration_ms": 5.71, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:31,011", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:31,012", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38E1B790>.", "duration_ms": 6.72, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:31,013", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:31,015", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38E18AF0>.", "duration_ms": 8.72, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:31,016", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38E1A440>.", "duration_ms": 7.61, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:31,102", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 91.37, "response_size": 4432, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:31,135", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 122.63, "response_size": 4432, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:31,316", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 311.63, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:31,319", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 314.53, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:31,321", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 313.1, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:31,324", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 318.23, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:38,193", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "INFO", "time": "2025-06-28 21:35:38,247", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 54.46, "response_size": 699, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:38,262", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:35:38,263", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:38,264", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C58F40>.", "duration_ms": 2.69, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:38,320", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 57.41, "response_size": 5132, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:38,377", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 115.44, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:41,274", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:41,278", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC7DC0>.", "duration_ms": 5.02, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:41,536", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 262.42, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:44,578", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:44,580", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2F0D0>.", "duration_ms": 2.12, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:44,766", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 187.49, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:47,628", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:47,630", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC51E0>.", "duration_ms": 2.02, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:47,814", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 186.44, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:50,982", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:50,985", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F72A10>.", "duration_ms": 2.53, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:51,167", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 185.17, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:53,979", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:53,981", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2CF70>.", "duration_ms": 1.02, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:54,175", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 195.13, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:57,210", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:57,210", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F72800>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:57,403", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 193.25, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:00,221", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:00,223", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2FF70>.", "duration_ms": 1.99, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:00,385", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 164.55, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:03,253", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:03,256", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F72AA0>.", "duration_ms": 3.68, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:03,448", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 195.2, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:06,234", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:06,235", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C5A980>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:06,419", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 184.88, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:09,239", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:09,240", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C5A260>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:09,476", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 236.59, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:12,244", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:12,246", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38ECE950>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:12,531", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 287.6, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:15,271", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:15,271", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2F790>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:15,559", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 287.93, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:18,464", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:18,466", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38ECE0B0>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:18,644", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 179.87, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:21,478", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:21,480", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC42E0>.", "duration_ms": 1.55, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:21,747", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 269.11, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:24,486", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:24,488", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C586D0>.", "duration_ms": 1.58, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:24,692", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 205.58, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:27,489", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:27,489", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39093100>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:27,668", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 178.79, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:30,491", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:30,495", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38EB9C90>.", "duration_ms": 4.11, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:30,687", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 196.53, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:33,500", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:33,502", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FDDDB0>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:33,688", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 188.61, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:36,508", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:36,509", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2F820>.", "duration_ms": 1.18, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:36,689", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 181.04, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:39,516", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:39,518", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FDE110>.", "duration_ms": 2.03, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:39,705", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 188.71, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:42,530", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:42,530", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39092F80>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:42,729", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 199.34, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:45,545", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:45,546", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2C3D0>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:45,776", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 230.73, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:48,556", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:48,560", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F722F0>.", "duration_ms": 4.55, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:48,786", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 230.24, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:51,556", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:51,557", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2F790>.", "duration_ms": 1.51, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:51,760", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 203.74, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:54,566", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:54,568", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F71EA0>.", "duration_ms": 2.06, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:54,768", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 201.44, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:57,584", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:57,586", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2F790>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:57,752", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 168.0, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:00,589", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:00,591", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F72830>.", "duration_ms": 2.19, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:00,789", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 200.05, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:03,599", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:03,601", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FDF310>.", "duration_ms": 2.02, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:03,813", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 214.17, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:06,596", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:06,599", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2C7F0>.", "duration_ms": 2.05, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:06,791", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 194.97, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:09,605", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:09,606", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38EB9CF0>.", "duration_ms": 1.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:09,815", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 209.89, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:12,611", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:12,613", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C58C70>.", "duration_ms": 1.98, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:12,800", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 189.33, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:15,612", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:15,613", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2F640>.", "duration_ms": 1.58, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:15,799", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 187.33, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:18,880", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:18,881", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38ECF8E0>.", "duration_ms": 1.02, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:19,072", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 191.87, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:21,891", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:21,893", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39093790>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:22,089", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 197.83, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:24,895", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:24,896", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C586D0>.", "duration_ms": 0.99, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:25,094", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 199.14, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:27,917", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:27,924", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC4640>.", "duration_ms": 6.07, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:28,282", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 365.48, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:31,022", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:31,023", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38ECC070>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:31,214", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 192.4, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:32,918", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:32,919", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FDFDC0>.", "duration_ms": 1.03, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:32,919", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:32,929", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC5AB0>.", "duration_ms": 10.26, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:32,929", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:37:32,929", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:37:32,938", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:37:32,950", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:32,951", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C390424A0>.", "duration_ms": 21.7, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:32,960", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F5E410>.", "duration_ms": 22.02, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:33,003", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 73.55, "response_size": 5132, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:33,015", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 64.66, "response_size": 5132, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:33,217", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 278.89, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:33,250", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 330.51, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:33,255", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 336.57, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:33,265", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 330.39, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:39,335", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "INFO", "time": "2025-06-28 21:37:39,390", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 56.11, "response_size": 707, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:39,416", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:37:39,418", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:39,423", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39041BD0>.", "duration_ms": 6.07, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:39,483", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 65.1, "response_size": 5840, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:39,552", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 135.86, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:42,619", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:42,623", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39087880>.", "duration_ms": 3.61, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:42,904", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 284.5, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:45,658", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:45,658", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3903D060>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:45,847", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 180.93, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:48,841", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:48,842", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39085F90>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:49,032", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 190.99, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:51,868", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:51,868", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3903DC90>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:52,069", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 200.68, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:54,878", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:54,879", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2FE50>.", "duration_ms": 1.65, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:55,096", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 218.4, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:37:57,891", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:37:57,893", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38D04250>.", "duration_ms": 1.99, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:37:58,319", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 428.26, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:01,128", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:01,130", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F41030>.", "duration_ms": 2.03, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:01,351", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 222.4, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:04,143", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:04,145", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38D05FC0>.", "duration_ms": 1.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:04,362", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 219.03, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:07,152", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:07,153", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F40400>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:07,366", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 214.35, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:10,164", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:10,166", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38D059C0>.", "duration_ms": 2.51, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:10,397", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 233.53, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:13,254", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:13,272", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2F370>.", "duration_ms": 17.86, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:13,810", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 555.77, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:16,182", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:16,184", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38EB9A80>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:16,399", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 217.28, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:19,198", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:19,199", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C390918D0>.", "duration_ms": 0.95, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:19,396", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 197.88, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:23,144", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/study-sets/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:38:23,147", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/study-sets/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:38:23,183", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/study-sets/", "status_code": 200, "duration_ms": 39.73, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:23,186", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/study-sets/", "status_code": 200, "duration_ms": 38.47, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:24,004", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:24,006", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2D1B0>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:24,191", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 186.97, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:27,024", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:27,026", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38D07100>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:27,228", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 204.24, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:30,164", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:30,166", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F09AE0>.", "duration_ms": 1.99, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:30,354", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 190.25, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:33,898", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:33,900", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3903C700>.", "duration_ms": 2.02, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:34,111", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 212.57, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:36,908", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:36,910", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38EB86D0>.", "duration_ms": 1.75, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:37,118", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 210.42, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:39,914", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:39,916", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38D04370>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:40,360", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 445.45, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:42,922", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:42,927", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38ECD4B0>.", "duration_ms": 5.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:43,263", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 340.53, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:45,928", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:45,930", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC71C0>.", "duration_ms": 2.62, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:46,259", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 331.38, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:49,157", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:49,159", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC5210>.", "duration_ms": 2.2, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:49,442", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 285.47, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:52,152", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:52,153", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38ECFFD0>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:52,473", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 320.46, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:55,399", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:55,402", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3903F0D0>.", "duration_ms": 2.54, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:55,689", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 290.14, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:38:58,400", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:38:58,402", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C390930D0>.", "duration_ms": 2.29, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:38:58,685", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 285.77, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:01,410", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:01,414", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39090D00>.", "duration_ms": 4.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:01,693", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 283.36, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:02,763", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/study-sets/", "user_agent": "python-requests/2.31.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:39:02,851", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/study-sets/", "status_code": 200, "duration_ms": 91.17, "response_size": 4634, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:04,426", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:04,428", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38EB82E0>.", "duration_ms": 3.06, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:04,738", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 312.76, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:07,430", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:07,432", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38D07700>.", "duration_ms": 1.75, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:07,748", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 317.98, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:10,432", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:10,435", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3903FDC0>.", "duration_ms": 3.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:10,713", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 279.62, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:13,432", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:13,432", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C390904C0>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:13,739", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 307.11, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:16,445", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:16,446", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38D06500>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:16,741", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 295.97, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:19,449", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:19,453", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39043010>.", "duration_ms": 3.82, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:19,846", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 395.28, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:22,458", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:22,461", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38BAFD60>.", "duration_ms": 2.9, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:22,752", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 294.71, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:25,470", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:25,472", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39040AF0>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:25,799", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 329.64, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:28,490", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:28,492", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2DFF0>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:28,785", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 295.35, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:31,720", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:31,722", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F43790>.", "duration_ms": 2.03, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:31,991", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 271.78, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:34,731", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:34,736", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39092290>.", "duration_ms": 3.43, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:35,024", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 292.36, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:37,740", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:37,744", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38D060E0>.", "duration_ms": 4.06, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:38,023", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 283.19, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:40,746", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:40,747", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F737C0>.", "duration_ms": 1.52, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:41,053", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 307.4, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:43,754", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:43,754", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C390432B0>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:44,013", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 259.39, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:46,761", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:46,761", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2FDF0>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:47,053", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 292.03, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:49,768", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:49,770", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F727D0>.", "duration_ms": 2.02, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:50,038", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 270.24, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:52,772", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:52,774", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC7400>.", "duration_ms": 2.02, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:53,024", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 252.05, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:55,775", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:55,776", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39092C50>.", "duration_ms": 0.98, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:56,033", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 258.26, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:39:58,788", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:39:58,791", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38D06DA0>.", "duration_ms": 2.66, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:39:59,044", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 255.85, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:01,788", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:01,790", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F736A0>.", "duration_ms": 1.73, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:02,044", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 256.07, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:04,788", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:04,791", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F72260>.", "duration_ms": 2.85, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:05,053", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 265.2, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:07,794", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:07,795", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38D05D50>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:08,074", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 281.06, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:10,805", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:10,809", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C390927A0>.", "duration_ms": 3.69, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:11,124", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 318.89, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:13,811", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:13,811", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38D06AA0>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:14,112", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 300.53, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:16,822", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:16,824", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3903FE20>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:17,079", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 257.83, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:19,832", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:19,835", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC52A0>.", "duration_ms": 3.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:20,098", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 265.67, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:22,836", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:22,838", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38D06CE0>.", "duration_ms": 2.03, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:23,169", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 333.5, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:25,841", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:25,842", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C390428C0>.", "duration_ms": 0.99, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:26,110", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 269.31, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:28,840", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:28,843", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC78B0>.", "duration_ms": 3.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:29,131", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 291.84, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:31,883", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:31,886", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F73D30>.", "duration_ms": 3.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:32,137", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 254.33, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:34,898", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:34,900", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39040190>.", "duration_ms": 2.04, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:35,147", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 247.54, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:37,898", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:37,899", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38DA85B0>.", "duration_ms": 1.21, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:38,183", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 285.34, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:40,908", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:40,911", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3903FBE0>.", "duration_ms": 2.92, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:41,220", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 311.47, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:43,906", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:43,908", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38DA97B0>.", "duration_ms": 2.27, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:44,200", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 293.54, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:46,916", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:46,918", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3903D6F0>.", "duration_ms": 1.54, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:47,225", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 309.43, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:49,919", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:49,921", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38D04A30>.", "duration_ms": 2.02, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:50,151", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 231.42, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:52,919", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:52,919", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2C700>.", "duration_ms": 1.16, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:53,135", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 216.68, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:55,926", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:55,928", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2C400>.", "duration_ms": 1.51, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:56,146", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 220.0, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:40:58,933", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:40:58,935", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38BAFE50>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:40:59,144", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 210.48, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:01,944", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:01,946", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F42200>.", "duration_ms": 1.02, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:02,136", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 191.38, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:04,955", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:04,956", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38D056F0>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:05,159", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 203.24, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:07,964", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:07,966", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38DAB160>.", "duration_ms": 3.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:08,169", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 206.44, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:10,964", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:10,966", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3903FC40>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:11,167", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 202.95, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:13,974", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:13,976", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38DA9A20>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:14,169", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 195.85, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:16,984", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:16,985", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F72080>.", "duration_ms": 1.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:17,192", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 208.06, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:19,996", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:19,997", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38DA8730>.", "duration_ms": 2.61, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:20,223", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 228.64, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:23,009", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:23,011", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38D067D0>.", "duration_ms": 2.05, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:23,197", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 187.64, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:26,020", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:26,022", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38DA89A0>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:26,247", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 225.54, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:29,033", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:29,035", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F730A0>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:29,229", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 196.33, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:32,040", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:32,042", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F73850>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:32,235", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 194.32, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:35,050", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:35,052", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38DA9750>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:35,241", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 191.58, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:38,059", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:38,061", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39087550>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:38,255", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 196.33, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:41,068", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:41,070", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39093B80>.", "duration_ms": 2.08, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:41,269", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 201.13, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:44,076", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:44,078", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38D05060>.", "duration_ms": 2.07, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:44,291", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 215.17, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:47,086", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:47,087", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C390847F0>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:47,317", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 231.91, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:50,088", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:50,090", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39091510>.", "duration_ms": 1.98, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:50,303", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 215.3, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:53,165", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:53,166", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39041390>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:53,371", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 205.94, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:56,250", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:56,252", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3903DCC0>.", "duration_ms": 2.5, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:56,450", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 200.18, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:56,647", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "INFO", "time": "2025-06-28 21:41:57,592", "module": "cache", "message": "Cache hit for flashcards with key ai_result:flashcards:bc67034f5423dadf"}
{"level": "INFO", "time": "2025-06-28 21:41:57,593", "module": "client", "message": "Using cached result for flashcards"}
{"level": "INFO", "time": "2025-06-28 21:41:57,606", "module": "tasks", "message": "Successfully parsed 10 flashcards"}
{"level": "INFO", "time": "2025-06-28 21:41:57,659", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/generate/flashcards/", "status_code": 200, "duration_ms": 1012.06, "response_size": 96, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:57,674", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/study-sets/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:41:57,720", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/study-sets/", "status_code": 200, "duration_ms": 45.62, "response_size": 9832, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:57,733", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/study-sets/0c025dc7-eeb7-4a2d-9c64-ddc56e69fa43/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:41:57,779", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/study-sets/0c025dc7-eeb7-4a2d-9c64-ddc56e69fa43/flashcards/", "status_code": 200, "duration_ms": 46.15, "response_size": 6824, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:41:59,260", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:41:59,262", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3AB23E50>.", "duration_ms": 1.76, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:41:59,487", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 227.23, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:42:02,301", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:42:02,303", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3AA2DA80>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:42:02,521", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 220.19, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:42:05,520", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:42:05,521", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3A9F5F30>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:42:05,770", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 250.01, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:42:08,526", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:42:08,529", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3AC7C310>.", "duration_ms": 3.07, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:42:08,746", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 219.55, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:42:11,540", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:42:11,541", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3A99ED10>.", "duration_ms": 1.58, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:42:11,750", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 210.65, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:42:14,545", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:42:14,546", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3AB60160>.", "duration_ms": 1.1, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:42:14,759", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 213.88, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:42:17,719", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:42:17,721", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3903DD20>.", "duration_ms": 1.68, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:42:17,923", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 203.85, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:42:20,730", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:42:20,731", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3B172C80>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:42:20,944", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 213.66, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:42:23,730", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:42:23,732", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3B3778B0>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:42:23,932", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 201.62, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:42:26,731", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:42:26,733", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3B3762C0>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:42:26,927", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 196.76, "response_size": 178857, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:42:29,818", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:42:29,819", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C3AA2ED70>.", "duration_ms": 1.07, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:42:30,023", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/97d52230-01b3-4149-8329-aad9985fd81a/", "status_code": 500, "duration_ms": 204.76, "response_size": 178857, "ip_address": "127.0.0.1"}"}
