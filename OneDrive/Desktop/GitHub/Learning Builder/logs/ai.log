{"level": "INFO", "time": "2025-06-28 19:24:23,261", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2047", "upload_size": 1806}"}
{"level": "INFO", "time": "2025-06-28 19:24:23,355", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 94.26, "response_size": 62, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:23,376", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:24:23,395", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:24:23,426", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "status_code": 200, "duration_ms": 50.32, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:23,438", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/flashcards/", "status_code": 200, "duration_ms": 42.83, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:26,618", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "ERROR", "time": "2025-06-28 19:24:26,678", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/", "status_code": 500, "duration_ms": 60.09, "response_size": 92, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:28,742", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:24:28,762", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:24:28,772", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "status_code": 200, "duration_ms": 30.08, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:28,811", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/questions/", "status_code": 200, "duration_ms": 49.32, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:29,748", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "ERROR", "time": "2025-06-28 19:24:29,799", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/", "status_code": 500, "duration_ms": 52.27, "response_size": 74, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:44,665", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "INFO", "time": "2025-06-28 19:24:44,721", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 57.42, "response_size": 62, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:44,737", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:24:44,775", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "status_code": 200, "duration_ms": 37.99, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:47,554", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "ERROR", "time": "2025-06-28 19:24:47,704", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "status_code": 500, "duration_ms": 150.63, "response_size": 92, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:24:49,062", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "ERROR", "time": "2025-06-28 19:24:49,259", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "status_code": 500, "duration_ms": 197.0, "response_size": 92, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:27:49,542", "module": "tasks", "message": "Successfully parsed 2 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,542", "module": "tasks", "message": "Parsing AI flashcards output: {"front": "API", "back": "Application Programming Interface"}..."}
{"level": "WARNING", "time": "2025-06-28 19:27:49,543", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,544", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,544", "module": "tasks", "message": "Parsing AI flashcards output: Random text without proper format..."}
{"level": "WARNING", "time": "2025-06-28 19:27:49,545", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,545", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,546", "module": "tasks", "message": "Parsing AI flashcards output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:27:49,546", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,547", "module": "tasks", "message": "Successfully parsed 0 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:27:49,550", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:27:49,551", "module": "tasks", "message": "Parsing AI questions output: Random question text..."}
{"level": "WARNING", "time": "2025-06-28 19:27:49,552", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:27:49,552", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:27:49,552", "module": "tasks", "message": "Parsing AI questions output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:27:49,552", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:27:49,553", "module": "tasks", "message": "Successfully parsed 0 questions"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,952", "module": "client", "message": "Failed to initialize OpenAI client: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,953", "module": "client", "message": "Fallback also failed: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "INFO", "time": "2025-06-28 19:29:00,957", "module": "tasks", "message": "Successfully parsed 2 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,958", "module": "tasks", "message": "Parsing AI flashcards output: {"front": "API", "back": "Application Programming Interface"}..."}
{"level": "WARNING", "time": "2025-06-28 19:29:00,958", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,959", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,959", "module": "tasks", "message": "Parsing AI flashcards output: Random text without proper format..."}
{"level": "WARNING", "time": "2025-06-28 19:29:00,959", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,960", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,960", "module": "tasks", "message": "Parsing AI flashcards output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:29:00,960", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,961", "module": "tasks", "message": "Successfully parsed 0 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:00,963", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:29:00,964", "module": "tasks", "message": "Parsing AI questions output: Random question text..."}
{"level": "WARNING", "time": "2025-06-28 19:29:00,965", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:29:00,965", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:29:00,966", "module": "tasks", "message": "Parsing AI questions output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:29:00,966", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:29:00,967", "module": "tasks", "message": "Successfully parsed 0 questions"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,967", "module": "client", "message": "Failed to initialize OpenAI client: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,968", "module": "client", "message": "Fallback also failed: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,972", "module": "client", "message": "Failed to initialize OpenAI client: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "ERROR", "time": "2025-06-28 19:29:00,973", "module": "client", "message": "Fallback also failed: Client.__init__() got an unexpected keyword argument 'proxies'"}
{"level": "INFO", "time": "2025-06-28 19:29:22,649", "module": "client", "message": "Making OpenAI API call for unknown"}
{"level": "INFO", "time": "2025-06-28 19:29:25,086", "module": "client", "message": "Successfully generated content with 12 characters"}
{"level": "INFO", "time": "2025-06-28 19:29:25,093", "module": "tasks", "message": "Successfully parsed 2 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,093", "module": "tasks", "message": "Parsing AI flashcards output: {"front": "API", "back": "Application Programming Interface"}..."}
{"level": "WARNING", "time": "2025-06-28 19:29:25,094", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,094", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,095", "module": "tasks", "message": "Parsing AI flashcards output: Random text without proper format..."}
{"level": "WARNING", "time": "2025-06-28 19:29:25,095", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,096", "module": "tasks", "message": "Successfully parsed 1 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,096", "module": "tasks", "message": "Parsing AI flashcards output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:29:25,097", "module": "tasks", "message": "All parsing strategies failed, creating fallback flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,097", "module": "tasks", "message": "Successfully parsed 0 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:25,100", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:29:25,100", "module": "tasks", "message": "Parsing AI questions output: Random question text..."}
{"level": "WARNING", "time": "2025-06-28 19:29:25,101", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:29:25,103", "module": "tasks", "message": "Successfully parsed 1 questions"}
{"level": "INFO", "time": "2025-06-28 19:29:25,104", "module": "tasks", "message": "Parsing AI questions output: ..."}
{"level": "WARNING", "time": "2025-06-28 19:29:25,105", "module": "tasks", "message": "All parsing strategies failed, creating fallback questions"}
{"level": "INFO", "time": "2025-06-28 19:29:25,105", "module": "tasks", "message": "Successfully parsed 0 questions"}
{"level": "INFO", "time": "2025-06-28 19:29:25,118", "module": "client", "message": "Making OpenAI API call for flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:30,431", "module": "client", "message": "Successfully generated flashcards with 1407 characters"}
{"level": "INFO", "time": "2025-06-28 19:29:30,434", "module": "tasks", "message": "Successfully parsed 10 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:29:30,447", "module": "client", "message": "Making OpenAI API call for questions"}
{"level": "INFO", "time": "2025-06-28 19:29:45,734", "module": "client", "message": "Successfully generated questions with 3273 characters"}
{"level": "INFO", "time": "2025-06-28 19:29:45,740", "module": "tasks", "message": "Successfully parsed 19 questions"}
{"level": "INFO", "time": "2025-06-28 19:30:04,229", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "INFO", "time": "2025-06-28 19:30:04,415", "module": "cache", "message": "Cache miss for flashcards with key ai_result:flashcards:bc67034f5423dadf"}
{"level": "INFO", "time": "2025-06-28 19:30:04,417", "module": "client", "message": "Making OpenAI API call for flashcards"}
{"level": "INFO", "time": "2025-06-28 19:30:10,205", "module": "cache", "message": "Cached flashcards result with key ai_result:flashcards:bc67034f5423dadf for 604800 seconds"}
{"level": "INFO", "time": "2025-06-28 19:30:10,205", "module": "client", "message": "Successfully generated flashcards with 1918 characters"}
{"level": "INFO", "time": "2025-06-28 19:30:10,406", "module": "tasks", "message": "Successfully parsed 10 flashcards"}
{"level": "INFO", "time": "2025-06-28 19:30:10,464", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/", "status_code": 200, "duration_ms": 6234.64, "response_size": 15, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:30:10,480", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:30:10,522", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/flashcards/", "status_code": 200, "duration_ms": 42.12, "response_size": 5124, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:30:31,351", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:30:31,352", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:30:31,382", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "status_code": 200, "duration_ms": 30.21, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:30:31,397", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "status_code": 200, "duration_ms": 45.01, "response_size": 2, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:30:32,411", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "INFO", "time": "2025-06-28 19:30:32,601", "module": "cache", "message": "Cache miss for questions with key ai_result:questions:abb5dfd3fb19bd4c"}
{"level": "INFO", "time": "2025-06-28 19:30:32,601", "module": "client", "message": "Making OpenAI API call for questions"}
{"level": "INFO", "time": "2025-06-28 19:30:48,883", "module": "cache", "message": "Cached questions result with key ai_result:questions:abb5dfd3fb19bd4c for 604800 seconds"}
{"level": "INFO", "time": "2025-06-28 19:30:48,884", "module": "client", "message": "Successfully generated questions with 4448 characters"}
{"level": "INFO", "time": "2025-06-28 19:30:48,902", "module": "tasks", "message": "Successfully parsed 20 questions"}
{"level": "INFO", "time": "2025-06-28 19:30:48,989", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/questions/", "status_code": 200, "duration_ms": 16577.25, "response_size": 15, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:30:49,003", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:30:49,032", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/questions/", "status_code": 200, "duration_ms": 29.33, "response_size": 10638, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:33:22,466", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/pdf/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:33:22,576", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/pdf/", "status_code": 200, "duration_ms": 111.24, "response_size": 0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:33:27,227", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/csv/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:33:27,263", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/csv/", "status_code": 200, "duration_ms": 35.83, "response_size": 0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 19:33:52,320", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/anki/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 19:33:52,455", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/export/anki/", "status_code": 200, "duration_ms": 134.81, "response_size": 0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:25:55,317", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/docs/", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; bg-BG) WindowsPowerShell/5.1.26100.4484", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:25:55,333", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/docs/", "status_code": 200, "duration_ms": 15.99, "response_size": 4640, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:31:46,969", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 20:31:46,998", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 31.04, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:32:19,958", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 20:32:19,964", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 5.69, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:32:29,652", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2051", "upload_size": 1806}"}
{"level": "WARNING", "time": "2025-06-28 20:32:29,657", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 6.53, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:32:31,611", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2051", "upload_size": 1806}"}
{"level": "WARNING", "time": "2025-06-28 20:32:31,618", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 7.0, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:38:32,827", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 20:38:32,828", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295565BAAA0>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:38:32,851", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 20:38:32,853", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295565BB5B0>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:38:33,059", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 206.94, "response_size": 170572, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:38:33,062", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 233.94, "response_size": 170572, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:39:17,035", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 20:39:17,037", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295564D9420>.", "duration_ms": 2.0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:39:17,037", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:39:17,039", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 20:39:17,047", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000029556433D30>.", "duration_ms": 9.53, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:39:17,049", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000029556701390>.", "duration_ms": 10.03, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:39:17,187", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 149.84, "response_size": 170572, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:39:17,191", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 155.88, "response_size": 170572, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:39:17,227", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 500, "duration_ms": 187.94, "response_size": 170527, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:42:24,144", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 20:42:24,152", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 8.55, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:46:57,720", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:46:57,721", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:46:57,721", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:46:57,721", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:46:57,722", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:46:57,724", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 20:46:57,733", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E285E0>.", "duration_ms": 12.99, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:57,733", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E2BD00>.", "duration_ms": 12.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:57,734", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E646D0>.", "duration_ms": 12.99, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:57,735", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E65000>.", "duration_ms": 14.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:57,735", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E65A50>.", "duration_ms": 13.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:57,736", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E66380>.", "duration_ms": 12.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:58,105", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 385.13, "response_size": 171328, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:58,108", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 386.15, "response_size": 171328, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:58,116", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 394.14, "response_size": 171328, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:58,120", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 500, "duration_ms": 399.15, "response_size": 171283, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:58,123", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 500, "duration_ms": 399.14, "response_size": 171283, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 20:46:58,125", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 404.14, "response_size": 171328, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:47:05,992", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 20:47:06,018", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 26.02, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:51:29,220", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:51:29,285", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 64.93, "response_size": 606, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:51:29,311", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "350", "upload_size": 74}"}
{"level": "WARNING", "time": "2025-06-28 20:51:29,342", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 32.2, "response_size": 77, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:51:53,280", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:51:53,317", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 36.42, "response_size": 606, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:51:53,337", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "779", "upload_size": 503}"}
{"level": "INFO", "time": "2025-06-28 20:51:53,403", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 66.78, "response_size": 410, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:51:53,412", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 20:51:53,461", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 48.97, "response_size": 1017, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 20:59:26,646", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 20:59:26,675", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 28.99, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:02:41,179", "module": "middleware", "message": "{"event": "request_start", "method": "OPTIONS", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0"}"}
{"level": "INFO", "time": "2025-06-28 21:02:41,180", "module": "middleware", "message": "{"event": "request_complete", "method": "OPTIONS", "path": "/api/documents/upload/", "status_code": 200, "duration_ms": 0.99, "response_size": 0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:02:41,204", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "1202", "upload_size": 905}"}
{"level": "INFO", "time": "2025-06-28 21:02:41,296", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 92.02, "response_size": 431, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:02:41,308", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/flashcards/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "ERROR", "time": "2025-06-28 21:02:42,269", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/flashcards/", "status_code": 500, "duration_ms": 961.22, "response_size": 225, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:02:42,278", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/quiz/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "ERROR", "time": "2025-06-28 21:02:42,286", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/quiz/", "status_code": 500, "duration_ms": 8.18, "response_size": 215, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:02:42,299", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/study-sets/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:02:42,305", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/study-sets/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B864909BD0>.", "duration_ms": 6.72, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:02:42,462", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/study-sets/", "status_code": 500, "duration_ms": 162.94, "response_size": 170417, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:04:32,797", "module": "middleware", "message": "{"event": "request_start", "method": "OPTIONS", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0"}"}
{"level": "INFO", "time": "2025-06-28 21:04:32,798", "module": "middleware", "message": "{"event": "request_complete", "method": "OPTIONS", "path": "/api/documents/upload/", "status_code": 200, "duration_ms": 1.0, "response_size": 0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:04:32,808", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "1202", "upload_size": 905}"}
{"level": "WARNING", "time": "2025-06-28 21:04:32,842", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 403, "duration_ms": 34.16, "response_size": 63, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:05:26,219", "module": "middleware", "message": "{"event": "request_start", "method": "OPTIONS", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0"}"}
{"level": "INFO", "time": "2025-06-28 21:05:26,221", "module": "middleware", "message": "{"event": "request_complete", "method": "OPTIONS", "path": "/api/documents/upload/", "status_code": 200, "duration_ms": 1.99, "response_size": 0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:05:26,232", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "1202", "upload_size": 905}"}
{"level": "WARNING", "time": "2025-06-28 21:05:26,270", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 403, "duration_ms": 38.11, "response_size": 63, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:05:48,927", "module": "middleware", "message": "{"event": "request_start", "method": "OPTIONS", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0"}"}
{"level": "INFO", "time": "2025-06-28 21:05:48,928", "module": "middleware", "message": "{"event": "request_complete", "method": "OPTIONS", "path": "/api/documents/upload/", "status_code": 200, "duration_ms": 2.02, "response_size": 0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:05:48,946", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "1202", "upload_size": 905}"}
{"level": "INFO", "time": "2025-06-28 21:05:49,022", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 76.97, "response_size": 439, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:05:49,031", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/962c7fb0-53ff-4efa-b8e1-f84e40b967ef/generate/flashcards/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "INFO", "time": "2025-06-28 21:05:50,193", "module": "cache", "message": "Cache miss for flashcards with key ai_result:flashcards:a31c2dcc858f8cff"}
{"level": "INFO", "time": "2025-06-28 21:05:50,196", "module": "client", "message": "Making OpenAI API call for flashcards"}
{"level": "INFO", "time": "2025-06-28 21:05:58,782", "module": "cache", "message": "Cached flashcards result with key ai_result:flashcards:a31c2dcc858f8cff for 604800 seconds"}
{"level": "INFO", "time": "2025-06-28 21:05:58,782", "module": "client", "message": "Successfully generated flashcards with 1381 characters"}
{"level": "INFO", "time": "2025-06-28 21:05:58,808", "module": "tasks", "message": "Successfully parsed 10 flashcards"}
{"level": "INFO", "time": "2025-06-28 21:05:58,882", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/962c7fb0-53ff-4efa-b8e1-f84e40b967ef/generate/flashcards/", "status_code": 200, "duration_ms": 9850.98, "response_size": 96, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:05:58,892", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/962c7fb0-53ff-4efa-b8e1-f84e40b967ef/generate/quiz/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": "0", "upload_size": 0}"}
{"level": "INFO", "time": "2025-06-28 21:05:58,959", "module": "cache", "message": "Cache miss for questions with key ai_result:questions:1ca776486586604a"}
{"level": "INFO", "time": "2025-06-28 21:05:58,961", "module": "client", "message": "Making OpenAI API call for questions"}
{"level": "INFO", "time": "2025-06-28 21:06:10,329", "module": "cache", "message": "Cached questions result with key ai_result:questions:1ca776486586604a for 604800 seconds"}
{"level": "INFO", "time": "2025-06-28 21:06:10,330", "module": "client", "message": "Successfully generated questions with 4044 characters"}
{"level": "INFO", "time": "2025-06-28 21:06:10,339", "module": "tasks", "message": "Successfully parsed 18 questions"}
{"level": "INFO", "time": "2025-06-28 21:06:10,440", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/962c7fb0-53ff-4efa-b8e1-f84e40b967ef/generate/quiz/", "status_code": 200, "duration_ms": 11548.0, "response_size": 144, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:06:10,446", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/study-sets/b147fce2-8bfe-44bc-be25-f5bc13d59599/quiz/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:06:10,523", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/study-sets/b147fce2-8bfe-44bc-be25-f5bc13d59599/quiz/", "status_code": 200, "duration_ms": 76.14, "response_size": 10160, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:06:10,529", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/study-sets/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:06:10,595", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/study-sets/", "status_code": 200, "duration_ms": 66.31, "response_size": 3704, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:06:10,601", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/study-sets/49d1142b-fb03-4d25-8e32-941045f78774/flashcards/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:06:10,643", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/study-sets/49d1142b-fb03-4d25-8e32-941045f78774/flashcards/", "status_code": 200, "duration_ms": 42.28, "response_size": 5727, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:07:26,010", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 21:07:26,013", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 4.01, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:07:28,546", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:07:28,548", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:07:28,549", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:07:28,551", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:07:28,553", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D135DB0>.", "duration_ms": 7.0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:07:28,554", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:07:28,554", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D313B50>.", "duration_ms": 7.08, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:07:28,556", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D2CE170>.", "duration_ms": 8.1, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:07:28,557", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D4365C0>.", "duration_ms": 6.11, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:07:28,570", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:07:28,613", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 59.13, "response_size": 1886, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:07:28,647", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 76.87, "response_size": 1886, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:07:28,782", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 231.77, "response_size": 171420, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:07:28,787", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 239.77, "response_size": 171420, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:07:28,791", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 245.79, "response_size": 171420, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:07:28,794", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 246.78, "response_size": 171420, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:07:34,614", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2051", "upload_size": 1806}"}
{"level": "WARNING", "time": "2025-06-28 21:07:34,617", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 3.97, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:07:35,830", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2049", "upload_size": 1806}"}
{"level": "WARNING", "time": "2025-06-28 21:07:35,832", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 3.0, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:07:46,898", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "2051", "upload_size": 1806}"}
{"level": "WARNING", "time": "2025-06-28 21:07:46,900", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 3.02, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:08:49,275", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:08:49,277", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:08:49,278", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2B7BD210>.", "duration_ms": 3.0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:08:49,304", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:08:49,305", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:08:49,306", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:08:49,307", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:08:49,309", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2DD4DBA0>.", "duration_ms": 4.63, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:08:49,311", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2DD4CE80>.", "duration_ms": 5.78, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:08:49,312", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2DD4EC20>.", "duration_ms": 6.38, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:08:49,320", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 43.78, "response_size": 1886, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:08:49,363", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 55.71, "response_size": 1886, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:08:49,413", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 137.83, "response_size": 171420, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:08:49,564", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 258.66, "response_size": 171420, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:08:49,569", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 264.7, "response_size": 171420, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:08:49,574", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 270.25, "response_size": 171420, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:09:18,941", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 21:09:18,948", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 8.01, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:09:56,942", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; bg-BG) WindowsPowerShell/5.1.26100.4484", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:09:57,000", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 58.27, "response_size": 1886, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:12,207", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:10:12,208", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:10:12,211", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E45F6C670>.", "duration_ms": 3.66, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:12,212", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:10:12,215", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E45F6C8B0>.", "duration_ms": 6.36, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:12,215", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:10:12,217", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:10:12,291", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E46029510>.", "duration_ms": 78.79, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:12,295", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:10:12,305", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E45EE2F20>.", "duration_ms": 89.56, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:12,357", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 139.88, "response_size": 1886, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:12,371", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 76.07, "response_size": 1886, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:10:12,592", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 384.83, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:10:12,595", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 380.07, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:10:12,599", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 386.59, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:10:12,602", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 393.33, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:26,073", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 21:10:26,076", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 4.06, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:41,446", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:10:41,487", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 40.77, "response_size": 1886, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:10:41,523", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "1197", "upload_size": 910}"}
{"level": "INFO", "time": "2025-06-28 21:10:41,574", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 51.68, "response_size": 421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:14:33,663", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:14:33,741", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 78.41, "response_size": 2308, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:14:33,762", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "1197", "upload_size": 910}"}
{"level": "INFO", "time": "2025-06-28 21:14:33,822", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 59.62, "response_size": 429, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:14:53,067", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.32.4", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:14:53,109", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 41.14, "response_size": 2738, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:14:53,127", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "1197", "upload_size": 910}"}
{"level": "INFO", "time": "2025-06-28 21:14:53,179", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 52.41, "response_size": 429, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:18:16,470", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:18:16,473", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:18:16,474", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:18:16,482", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:18:16,483", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5D9960>.", "duration_ms": 12.6, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:18:16,484", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5DA380>.", "duration_ms": 11.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:18:16,484", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5DAA10>.", "duration_ms": 10.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:18:16,487", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5DB670>.", "duration_ms": 5.0, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:18:16,502", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:18:16,517", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:18:16,650", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 148.09, "response_size": 3168, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:18:16,652", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 134.19, "response_size": 3168, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:18:16,832", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 350.59, "response_size": 171633, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:18:16,834", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 361.6, "response_size": 171633, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:18:16,843", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 369.68, "response_size": 171633, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:18:16,847", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 377.26, "response_size": 171633, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:18:22,585", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "WARNING", "time": "2025-06-28 21:18:22,588", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 4.01, "response_size": 37, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:34:02,734", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.31.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:34:02,786", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 53.61, "response_size": 3168, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:34:02,812", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "1197", "upload_size": 910}"}
{"level": "INFO", "time": "2025-06-28 21:34:02,852", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 40.78, "response_size": 429, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:34:51,544", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.31.0", "ip_address": "127.0.0.1", "content_length": "223", "upload_size": 44}"}
{"level": "WARNING", "time": "2025-06-28 21:34:51,552", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 400, "duration_ms": 8.14, "response_size": 77, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:02,474", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "python-requests/2.31.0", "ip_address": "127.0.0.1", "content_length": "629", "upload_size": 450}"}
{"level": "INFO", "time": "2025-06-28 21:35:02,536", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 62.08, "response_size": 412, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:09,460", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "python-requests/2.31.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:35:09,509", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 48.7, "response_size": 4011, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:09,524", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "1197", "upload_size": 910}"}
{"level": "INFO", "time": "2025-06-28 21:35:09,565", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 40.93, "response_size": 420, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:31,004", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:35:31,005", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:35:31,007", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:35:31,008", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:31,010", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38EE0280>.", "duration_ms": 5.71, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:31,011", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:31,012", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38E1B790>.", "duration_ms": 6.72, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:31,013", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:31,015", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38E18AF0>.", "duration_ms": 8.72, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:31,016", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38E1A440>.", "duration_ms": 7.61, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:31,102", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 91.37, "response_size": 4432, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:31,135", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 122.63, "response_size": 4432, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:31,316", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 311.63, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:31,319", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 314.53, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:31,321", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 313.1, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:31,324", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 318.23, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:38,193", "module": "middleware", "message": "{"event": "request_start", "method": "POST", "path": "/api/documents/upload/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": "98457", "upload_size": 98163}"}
{"level": "INFO", "time": "2025-06-28 21:35:38,247", "module": "middleware", "message": "{"event": "request_complete", "method": "POST", "path": "/api/documents/upload/", "status_code": 201, "duration_ms": 54.46, "response_size": 699, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:38,262", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/courses/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "INFO", "time": "2025-06-28 21:35:38,263", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:38,264", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/courses/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C58F40>.", "duration_ms": 2.69, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:38,320", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/", "status_code": 200, "duration_ms": 57.41, "response_size": 5132, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:38,377", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/courses/", "status_code": 500, "duration_ms": 115.44, "response_size": 170877, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:41,274", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:41,278", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC7DC0>.", "duration_ms": 5.02, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:41,536", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 262.42, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:44,578", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:44,580", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2F0D0>.", "duration_ms": 2.12, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:44,766", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 187.49, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:47,628", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:47,630", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC51E0>.", "duration_ms": 2.02, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:47,814", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 186.44, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:50,982", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:50,985", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F72A10>.", "duration_ms": 2.53, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:51,167", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 185.17, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:53,979", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:53,981", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2CF70>.", "duration_ms": 1.02, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:54,175", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 195.13, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:35:57,210", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:35:57,210", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F72800>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:35:57,403", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 193.25, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:00,221", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:00,223", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2FF70>.", "duration_ms": 1.99, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:00,385", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 164.55, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:03,253", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:03,256", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F72AA0>.", "duration_ms": 3.68, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:03,448", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 195.2, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:06,234", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:06,235", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C5A980>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:06,419", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 184.88, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:09,239", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:09,240", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C5A260>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:09,476", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 236.59, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:12,244", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:12,246", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38ECE950>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:12,531", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 287.6, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:15,271", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:15,271", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2F790>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:15,559", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 287.93, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:18,464", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:18,466", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38ECE0B0>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:18,644", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 179.87, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:21,478", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:21,480", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FC42E0>.", "duration_ms": 1.55, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:21,747", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 269.11, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:24,486", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:24,488", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38C586D0>.", "duration_ms": 1.58, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:24,692", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 205.58, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:27,489", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:27,489", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39093100>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:27,668", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 178.79, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:30,491", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:30,495", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38EB9C90>.", "duration_ms": 4.11, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:30,687", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 196.53, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:33,500", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:33,502", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FDDDB0>.", "duration_ms": 2.01, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:33,688", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 188.61, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:36,508", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:36,509", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2F820>.", "duration_ms": 1.18, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:36,689", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 181.04, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:39,516", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:39,518", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38FDE110>.", "duration_ms": 2.03, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:39,705", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 188.71, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:42,530", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:42,530", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C39092F80>.", "duration_ms": 0.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:42,729", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 199.34, "response_size": 178421, "ip_address": "127.0.0.1"}"}
{"level": "INFO", "time": "2025-06-28 21:36:45,545", "module": "middleware", "message": "{"event": "request_start", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "ip_address": "127.0.0.1", "content_length": ""}"}
{"level": "ERROR", "time": "2025-06-28 21:36:45,546", "module": "middleware", "message": "{"event": "request_exception", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "exception_type": "TypeError", "exception_message": "Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000016C38F2C3D0>.", "duration_ms": 1.0, "ip_address": "127.0.0.1"}"}
{"level": "ERROR", "time": "2025-06-28 21:36:45,776", "module": "middleware", "message": "{"event": "request_complete", "method": "GET", "path": "/api/documents/0cb5d246-5289-43cc-be07-6e9bbed8ad65/", "status_code": 500, "duration_ms": 230.73, "response_size": 178421, "ip_address": "127.0.0.1"}"}
