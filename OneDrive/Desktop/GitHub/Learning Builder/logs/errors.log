ERROR 2025-06-28 19:24:26,679 log 14148 3604 Internal Server Error: /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/
ERROR 2025-06-28 19:24:26,680 basehttp 14148 3604 "POST /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/flashcards/ HTTP/1.1" 500 92
ERROR 2025-06-28 19:24:29,800 log 14148 21056 Internal Server Error: /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/
ERROR 2025-06-28 19:24:29,801 basehttp 14148 21056 "POST /api/documents/7ff507b5-2c49-48a9-9bd4-c2b10fa72bec/generate/questions/ HTTP/1.1" 500 74
ERROR 2025-06-28 19:24:47,704 log 14148 20312 Internal Server Error: /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/
ERROR 2025-06-28 19:24:47,710 basehttp 14148 20312 "POST /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/ HTTP/1.1" 500 92
ERROR 2025-06-28 19:24:49,260 log 14148 19040 Internal Server Error: /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/
ERROR 2025-06-28 19:24:49,261 basehttp 14148 19040 "POST /api/documents/ff88e3d7-f197-4cbb-be11-ed302a8d78bc/generate/flashcards/ HTTP/1.1" 500 92
ERROR 2025-06-28 20:38:33,047 log 13688 3240 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295565BB5B0>.
ERROR 2025-06-28 20:38:33,050 log 13688 3056 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295565BAAA0>.
ERROR 2025-06-28 20:38:33,061 basehttp 13688 3240 "GET /api/courses/ HTTP/1.1" 500 170572
ERROR 2025-06-28 20:38:33,064 basehttp 13688 3056 "GET /api/courses/ HTTP/1.1" 500 170572
ERROR 2025-06-28 20:39:17,158 log 13688 4340 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000029556433D30>.
ERROR 2025-06-28 20:39:17,181 log 13688 21460 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x00000295564D9420>.
ERROR 2025-06-28 20:39:17,196 basehttp 13688 4340 "GET /api/courses/ HTTP/1.1" 500 170572
ERROR 2025-06-28 20:39:17,205 basehttp 13688 21460 "GET /api/courses/ HTTP/1.1" 500 170572
ERROR 2025-06-28 20:39:17,219 log 13688 6464 Internal Server Error: /api/documents/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 203, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 66, in get_queryset
    queryset = Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000029556701390>.
ERROR 2025-06-28 20:39:17,230 basehttp 13688 6464 "GET /api/documents/ HTTP/1.1" 500 170527
ERROR 2025-06-28 20:46:58,017 log 3360 18012 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E285E0>.
ERROR 2025-06-28 20:46:58,034 log 3360 4820 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E65A50>.
ERROR 2025-06-28 20:46:58,068 log 3360 14152 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E646D0>.
ERROR 2025-06-28 20:46:58,072 log 3360 10548 Internal Server Error: /api/documents/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 203, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 66, in get_queryset
    queryset = Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E65000>.
ERROR 2025-06-28 20:46:58,076 log 3360 7528 Internal Server Error: /api/documents/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 203, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 66, in get_queryset
    queryset = Document.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E66380>.
ERROR 2025-06-28 20:46:58,094 log 3360 17432 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 40, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001CAA4E2BD00>.
ERROR 2025-06-28 20:46:58,119 basehttp 3360 18012 "GET /api/courses/ HTTP/1.1" 500 171328
ERROR 2025-06-28 20:46:58,119 basehttp 3360 4820 "GET /api/courses/ HTTP/1.1" 500 171328
ERROR 2025-06-28 20:46:58,125 basehttp 3360 14152 "GET /api/courses/ HTTP/1.1" 500 171328
ERROR 2025-06-28 20:46:58,128 basehttp 3360 10548 "GET /api/documents/ HTTP/1.1" 500 171283
ERROR 2025-06-28 20:46:58,129 basehttp 3360 7528 "GET /api/documents/ HTTP/1.1" 500 171283
ERROR 2025-06-28 20:46:58,132 basehttp 3360 17432 "GET /api/courses/ HTTP/1.1" 500 171328
ERROR 2025-06-28 21:02:42,269 log 9064 13624 Internal Server Error: /api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/flashcards/
ERROR 2025-06-28 21:02:42,274 basehttp 9064 13624 "POST /api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/flashcards/ HTTP/1.1" 500 225
ERROR 2025-06-28 21:02:42,290 log 9064 20384 Internal Server Error: /api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/quiz/
ERROR 2025-06-28 21:02:42,290 basehttp 9064 20384 "POST /api/documents/9b174874-1861-4fa8-94d8-c82f9fbf4265/generate/quiz/ HTTP/1.1" 500 215
ERROR 2025-06-28 21:02:42,446 log 9064 9000 Internal Server Error: /api/study-sets/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 203, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 115, in get_queryset
    queryset = StudySet.objects.filter(document__owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B864909BD0>.
ERROR 2025-06-28 21:02:42,467 basehttp 9064 9000 "GET /api/study-sets/?document=9b174874-1861-4fa8-94d8-c82f9fbf4265 HTTP/1.1" 500 170417
ERROR 2025-06-28 21:07:28,739 log 16340 7216 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D4365C0>.
ERROR 2025-06-28 21:07:28,745 log 16340 10024 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D313B50>.
ERROR 2025-06-28 21:07:28,755 log 16340 11124 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D135DB0>.
ERROR 2025-06-28 21:07:28,783 log 16340 7856 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2D2CE170>.
ERROR 2025-06-28 21:07:28,789 basehttp 16340 7216 "GET /api/courses/ HTTP/1.1" 500 171420
ERROR 2025-06-28 21:07:28,790 basehttp 16340 10024 "GET /api/courses/ HTTP/1.1" 500 171420
ERROR 2025-06-28 21:07:28,798 basehttp 16340 11124 "GET /api/courses/ HTTP/1.1" 500 171420
ERROR 2025-06-28 21:07:28,800 basehttp 16340 7856 "GET /api/courses/ HTTP/1.1" 500 171420
ERROR 2025-06-28 21:08:49,404 log 16340 6728 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2B7BD210>.
ERROR 2025-06-28 21:08:49,424 basehttp 16340 6728 "GET /api/courses/ HTTP/1.1" 500 171420
ERROR 2025-06-28 21:08:49,531 log 16340 1628 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2DD4EC20>.
ERROR 2025-06-28 21:08:49,535 log 16340 19092 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2DD4CE80>.
ERROR 2025-06-28 21:08:49,564 log 16340 6484 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000001EB2DD4DBA0>.
ERROR 2025-06-28 21:08:49,573 basehttp 16340 1628 "GET /api/courses/ HTTP/1.1" 500 171420
ERROR 2025-06-28 21:08:49,576 basehttp 16340 19092 "GET /api/courses/ HTTP/1.1" 500 171420
ERROR 2025-06-28 21:08:49,578 basehttp 16340 6484 "GET /api/courses/ HTTP/1.1" 500 171420
ERROR 2025-06-28 21:10:12,555 log 18476 6840 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E45F6C670>.
ERROR 2025-06-28 21:10:12,576 log 18476 10712 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E45EE2F20>.
ERROR 2025-06-28 21:10:12,583 log 18476 4500 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E46029510>.
ERROR 2025-06-28 21:10:12,586 log 18476 20260 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 42, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x0000027E45F6C8B0>.
ERROR 2025-06-28 21:10:12,602 basehttp 18476 6840 "GET /api/courses/ HTTP/1.1" 500 170877
ERROR 2025-06-28 21:10:12,602 basehttp 18476 4500 "GET /api/courses/ HTTP/1.1" 500 170877
ERROR 2025-06-28 21:10:12,602 basehttp 18476 10712 "GET /api/courses/ HTTP/1.1" 500 170877
ERROR 2025-06-28 21:10:12,610 basehttp 18476 20260 "GET /api/courses/ HTTP/1.1" 500 170877
ERROR 2025-06-28 21:18:16,752 log 1320 2512 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 45, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5DB670>.
ERROR 2025-06-28 21:18:16,781 log 1320 19796 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 45, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5DA380>.
ERROR 2025-06-28 21:18:16,797 log 1320 17340 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 45, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5DAA10>.
ERROR 2025-06-28 21:18:16,817 log 1320 18456 Internal Server Error: /api/courses/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\contrib\auth\models.py", line 549, in __int__
    raise TypeError(
TypeError: Cannot cast AnonymousUser to int. Are you trying to use it in place of User?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\generics.py", line 243, in get
    return self.list(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\documents\views.py", line 45, in get_queryset
    return Course.objects.filter(owner=self.request.user)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\OneDrive\Desktop\GitHub\Learning Builder\.venv\lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <django.contrib.auth.models.AnonymousUser object at 0x000002B08B5D9960>.
ERROR 2025-06-28 21:18:16,840 basehttp 1320 2512 "GET /api/courses/ HTTP/1.1" 500 171633
ERROR 2025-06-28 21:18:16,842 basehttp 1320 19796 "GET /api/courses/ HTTP/1.1" 500 171633
ERROR 2025-06-28 21:18:16,852 basehttp 1320 17340 "GET /api/courses/ HTTP/1.1" 500 171633
ERROR 2025-06-28 21:18:16,855 basehttp 1320 18456 "GET /api/courses/ HTTP/1.1" 500 171633
