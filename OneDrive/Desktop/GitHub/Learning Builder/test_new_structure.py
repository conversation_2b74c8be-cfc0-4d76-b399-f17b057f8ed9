#!/usr/bin/env python
"""
Тестов скрипт за новата структура на базата данни.
"""
import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Learning_Builder.settings')
django.setup()

from django.contrib.auth.models import User
from documents.models import UserProfile, Course, Document, StudySet, Quiz, Question, Flashcard
from django.core.files.uploadedfile import SimpleUploadedFile

def test_new_structure():
    """Тества новата структура на базата данни."""
    print("🧪 Тестване на новата структура...")
    
    # 1. Намиране на тестовия потребител
    try:
        user = User.objects.get(username='testuser')
        print(f"✅ Намерен потребител: {user.username}")
    except User.DoesNotExist:
        user = User.objects.create_user('testuser', '<EMAIL>', 'testpass123')
        print(f"✅ Създаден нов потребител: {user.username}")
    
    # 2. Създаване на потребителски профил
    profile, created = UserProfile.objects.get_or_create(user=user)
    if created:
        print("✅ Създаден потребителски профил")
    else:
        print("✅ Намерен съществуващ профил")
    
    # 3. Създаване на курс
    course, created = Course.objects.get_or_create(
        owner=user,
        name="Тестов курс",
        defaults={
            'description': 'Курс за тестване на новата структура',
            'color': '#e74c3c'
        }
    )
    if created:
        print("✅ Създаден курс")
    else:
        print("✅ Намерен съществуващ курс")
    
    # 4. Създаване на документ
    test_content_str = "This is a test document for checking the new structure. Python is a programming language. Django is a web framework."
    test_content = test_content_str.encode('utf-8')
    test_file = SimpleUploadedFile("test.txt", test_content, content_type="text/plain")
    
    document, created = Document.objects.get_or_create(
        owner=user,
        title="Тестов документ",
        defaults={
            'course': course,
            'file': test_file,
            'file_size': len(test_content),
            'file_type': 'txt',
            'status': 'ready',
            'extracted_text': test_content_str
        }
    )
    if created:
        print("✅ Създаден документ")
    else:
        print("✅ Намерен съществуващ документ")
    
    # 5. Създаване на study set за флашкарти
    flashcard_set, created = StudySet.objects.get_or_create(
        document=document,
        name="Тестови флашкарти",
        defaults={
            'content_type': 'flashcards',
            'description': 'Тестови флашкарти за проверка'
        }
    )
    if created:
        print("✅ Създаден study set за флашкарти")
    else:
        print("✅ Намерен съществуващ study set за флашкарти")
    
    # 6. Създаване на флашкарти
    test_flashcards = [
        {'front': 'Какво е Python?', 'back': 'Програмен език'},
        {'front': 'Какво е Django?', 'back': 'Web framework за Python'},
    ]
    
    for flashcard_data in test_flashcards:
        flashcard, created = Flashcard.objects.get_or_create(
            study_set=flashcard_set,
            front=flashcard_data['front'],
            defaults={'back': flashcard_data['back']}
        )
        if created:
            print(f"✅ Създадена флашкарта: {flashcard.front}")
    
    # 7. Създаване на study set за тест
    quiz_set, created = StudySet.objects.get_or_create(
        document=document,
        name="Тестов quiz",
        defaults={
            'content_type': 'quiz',
            'description': 'Тестов quiz за проверка'
        }
    )
    if created:
        print("✅ Създаден study set за quiz")
    else:
        print("✅ Намерен съществуващ study set за quiz")
    
    # 8. Създаване на quiz
    quiz, created = Quiz.objects.get_or_create(
        study_set=quiz_set,
        title="Тестов quiz",
        defaults={
            'description': 'Тестов quiz за проверка на структурата',
            'shuffle_questions': True,
            'show_correct_answers': True
        }
    )
    if created:
        print("✅ Създаден quiz")
    else:
        print("✅ Намерен съществуващ quiz")
    
    # 9. Създаване на въпроси
    test_questions = [
        {
            'question_text': 'Какво е Python?',
            'choices': ['A) Змия', 'B) Програмен език', 'C) Животно', 'D) Нищо от изброените'],
            'correct_choice': 'B) Програмен език',
            'explanation': 'Python е популярен програмен език'
        },
        {
            'question_text': 'Какво е Django?',
            'choices': ['A) База данни', 'B) Web framework', 'C) Операционна система', 'D) Браузър'],
            'correct_choice': 'B) Web framework',
            'explanation': 'Django е web framework за Python'
        }
    ]
    
    for i, question_data in enumerate(test_questions):
        question, created = Question.objects.get_or_create(
            quiz=quiz,
            question_text=question_data['question_text'],
            defaults={
                'choices': question_data['choices'],
                'correct_choice': question_data['correct_choice'],
                'explanation': question_data['explanation'],
                'order': i,
                'points': 1
            }
        )
        if created:
            print(f"✅ Създаден въпрос: {question.question_text}")
    
    # 10. Статистики
    print("\n📊 Статистики:")
    print(f"   Потребители: {User.objects.count()}")
    print(f"   Профили: {UserProfile.objects.count()}")
    print(f"   Курсове: {Course.objects.count()}")
    print(f"   Документи: {Document.objects.count()}")
    print(f"   Study Sets: {StudySet.objects.count()}")
    print(f"   Флашкарти: {Flashcard.objects.count()}")
    print(f"   Quizzes: {Quiz.objects.count()}")
    print(f"   Въпроси: {Question.objects.count()}")
    
    print("\n🎉 Тестването завърши успешно!")
    print("Новата структура работи перфектно!")

if __name__ == "__main__":
    test_new_structure()
