import React, { useState, useEffect } from 'react';
import UploadForm from '../components/UploadForm';
import DocumentTabs from '../components/DocumentTabs';
import { getCourses, getDocuments, type Course, type Document } from '../api/documents';

const Home: React.FC = () => {
  const [currentDocument, setCurrentDocument] = useState<Document | null>(null);
  const [courses, setCourses] = useState<Course[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<string>('');

  // Load courses and documents on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [coursesData, documentsData] = await Promise.all([
        getCourses(),
        getDocuments()
      ]);
      setCourses(coursesData);
      setDocuments(documentsData);
    } catch (err) {
      console.error('Error loading data:', err);
    }
  };

  const handleUpload = (document: Document) => {
    setCurrentDocument(document);
    // Refresh documents list
    loadData();
  };

  const handleDocumentSelect = (document: Document) => {
    setCurrentDocument(document);
  };

  const filteredDocuments = selectedCourse
    ? documents.filter(doc => doc.course === selectedCourse)
    : documents;

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>🎓 AI Tutor Platform</h1>
      <p>Качете документ и генерирайте флашкарти и тестове с изкуствен интелект</p>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginTop: '20px' }}>
        {/* Upload Section */}
        <div>
          <UploadForm onUpload={handleUpload} />
        </div>

        {/* Documents List */}
        <div>
          <h3>Ваши документи</h3>

          {courses.length > 0 && (
            <div style={{ marginBottom: '15px' }}>
              <label htmlFor="course-filter" style={{ display: 'block', marginBottom: '5px' }}>
                Филтър по курс:
              </label>
              <select
                id="course-filter"
                value={selectedCourse}
                onChange={(e) => setSelectedCourse(e.target.value)}
                style={{ padding: '5px', minWidth: '200px' }}
              >
                <option value="">-- Всички курсове --</option>
                {courses.map(course => (
                  <option key={course.id} value={course.id}>
                    {course.name} ({course.documents_count})
                  </option>
                ))}
              </select>
            </div>
          )}

          <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
            {filteredDocuments.length === 0 ? (
              <p style={{ color: '#666' }}>Няма качени документи</p>
            ) : (
              filteredDocuments.map(doc => (
                <div
                  key={doc.id}
                  onClick={() => handleDocumentSelect(doc)}
                  style={{
                    padding: '10px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    marginBottom: '10px',
                    cursor: 'pointer',
                    backgroundColor: currentDocument?.id === doc.id ? '#e3f2fd' : '#f8f9fa'
                  }}
                >
                  <h4 style={{ margin: '0 0 5px 0' }}>{doc.title}</h4>
                  <p style={{ margin: '0', fontSize: '0.9em', color: '#666' }}>
                    Статус: <span style={{
                      color: doc.status === 'ready' ? 'green' : doc.status === 'failed' ? 'red' : 'orange'
                    }}>
                      {doc.status === 'ready' ? 'Готов' : doc.status === 'failed' ? 'Неуспешен' : 'Обработва се'}
                    </span>
                  </p>
                  {doc.course_name && (
                    <p style={{ margin: '0', fontSize: '0.8em', color: '#888' }}>
                      Курс: {doc.course_name}
                    </p>
                  )}
                  <p style={{ margin: '0', fontSize: '0.8em', color: '#888' }}>
                    {doc.file_size_mb} MB • {new Date(doc.created_at).toLocaleDateString()}
                  </p>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Document Tabs */}
      {currentDocument && (currentDocument.status === 'ready' || currentDocument.status === 'uploaded') && (
        <div style={{ marginTop: '30px' }}>
          <DocumentTabs documentId={currentDocument.id} />
        </div>
      )}
    </div>
  );
};

export default Home;
