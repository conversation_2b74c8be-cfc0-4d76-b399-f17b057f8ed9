import React, { useEffect, useState } from 'react';
import { generateExplanations } from '../api/documents';

type Explanation = {
  id: number;
  document: string;
  term: string;
  definition: string;
  created_at: string;
  updated_at: string;
};

interface Props {
  documentId: string;
}

const ExplanationList: React.FC<Props> = ({ documentId }) => {
  const [explanations, setExplanations] = useState<Explanation[]>([]);
  const [loading, setLoading] = useState(true);
  const [genLoading, setGenLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const fetchExplanations = () => {
    setLoading(true);
    fetch(`/api/documents/${documentId}/explanations/`)
      .then(res => res.json())
      .then(data => {
        setExplanations(data);
        setLoading(false);
      });
  };

  useEffect(() => {
    fetchExplanations();
    // eslint-disable-next-line
  }, [documentId]);

  const handleGenerate = async () => {
    setGenLoading(true);
    setError(null);
    setSuccess(null);
    try {
      await generateExplanations(documentId);
      setSuccess('Обясненията са генерирани успешно!');
      fetchExplanations();
    } catch {
      setError('Грешка при генериране на обяснения.');
    } finally {
      setGenLoading(false);
    }
  };

  if (loading) return <div>Зареждане на обяснения...</div>;

  return (
    <div>
      <h3>Обяснения</h3>
      {error && <div style={{color: 'red'}}>{error}</div>}
      {success && <div style={{color: 'green'}}>{success}</div>}
      {!explanations.length && (
        <div>
          <div>Няма обяснения за този документ.</div>
          <button onClick={handleGenerate} disabled={genLoading} style={{marginTop: 8}}>
            {genLoading ? 'Генериране...' : 'Генерирай обяснения'}
          </button>
        </div>
      )}
      {!!explanations.length && (
        <>
          <button onClick={handleGenerate} disabled={genLoading} style={{marginBottom: 8}}>
            {genLoading ? 'Генериране...' : 'Генерирай нови обяснения'}
          </button>
          <ul>
            {explanations.map((explanation) => (
              <li key={explanation.id} style={{marginBottom: 16}}>
                <strong>{explanation.term}:</strong> {explanation.definition}
              </li>
            ))}
          </ul>
        </>
      )}
    </div>
  );
};

export default ExplanationList;
