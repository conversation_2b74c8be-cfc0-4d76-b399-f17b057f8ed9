import React from 'react';
import { type Flashcard } from '../api/documents';

interface Props {
  flashcards: Flashcard[];
  loading: boolean;
  error: string | null;
  success: string | null;
  genLoading: boolean;
  onGenerate: () => void;
  index: number;
  setIndex: (i: number) => void;
}

const FlashcardView: React.FC<Props> = ({ flashcards, loading, error, success, genLoading, onGenerate, index, setIndex }) => {
  if (loading) return <div>Зареждане на флашкарти...</div>;

  if (!flashcards.length) {
    return (
      <div>
        <h3>Флашкарти</h3>
        {error && <div style={{color: 'red'}}>{error}</div>}
        {success && <div style={{color: 'green'}}>{success}</div>}
        <div>Няма флашкарти за този документ.</div>
        <button onClick={onGenerate} disabled={genLoading} style={{marginTop: 8}}>
          {genLoading ? 'Генериране...' : 'Генерирай флашкарти'}
        </button>
      </div>
    );
  }

  const card = flashcards[index];

  return (
    <div>
      <h3>Флашкарти</h3>
      <div style={{marginBottom: 8, fontWeight: 'bold'}}>Флашкарта {index + 1} от {flashcards.length}</div>
      {error && <div style={{color: 'red'}}>{error}</div>}
      {success && <div style={{color: 'green'}}>{success}</div>}
      <div style={{border: '1px solid #ccc', padding: 16, marginBottom: 8}}>
        <div><b>Въпрос:</b> {card.front}</div>
        <div><b>Отговор:</b> {card.back}</div>
      </div>
      <button onClick={() => setIndex((index - 1 + flashcards.length) % flashcards.length)}>Назад</button>
      <button onClick={() => setIndex((index + 1) % flashcards.length)}>Напред</button>
    </div>
  );
};

export default FlashcardView;
