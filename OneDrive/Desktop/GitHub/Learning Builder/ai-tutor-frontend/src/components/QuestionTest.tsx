import React, { useEffect, useState } from 'react';
import { getStudySetQuiz, type Quiz, type Question, type StudySet } from '../api/documents';

interface Props {
  documentId: string;
  quizStudySet?: StudySet;
}

const LETTERS = ['A', 'B', 'C', 'D'];

const QuestionTest: React.FC<Props> = ({ documentId, quizStudySet }) => {
  const [quiz, setQuiz] = useState<Quiz | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [answers, setAnswers] = useState<{ [id: string]: string }>({});
  const [showResult, setShowResult] = useState<{ [id: string]: boolean }>({});
  const [showFinal, setShowFinal] = useState(false);

  const loadQuiz = async () => {
    if (!quizStudySet) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      // Get quiz from study set
      const quizData = await getStudySetQuiz(quizStudySet.id);
      setQuiz(quizData);
      setQuestions(quizData.questions);
    } catch (err) {
      console.error('Error loading quiz:', err);
      setError('Грешка при зареждане на теста');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadQuiz();
    // eslint-disable-next-line
  }, [quizStudySet]);

  const handleSelect = (qid: string, choice: string) => {
    setAnswers(prev => ({ ...prev, [qid]: choice }));
  };

  const handleCheck = (qid: string) => {
    setShowResult(prev => ({ ...prev, [qid]: true }));
  };

  const handleRestart = () => {
    setAnswers({});
    setShowResult({});
    setShowFinal(false);
  };

  const calculateScore = () => {
    let correct = 0;
    questions.forEach(q => {
      if (answers[q.id] === q.correct_choice) {
        correct++;
      }
    });
    return { correct, total: questions.length };
  };

  const handleFinish = () => {
    setShowFinal(true);
    // Show all results
    const allResults: { [id: string]: boolean } = {};
    questions.forEach(q => {
      allResults[q.id] = true;
    });
    setShowResult(allResults);
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        ⏳ Зарежда тест...
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ color: 'red', padding: '20px', backgroundColor: '#ffe6e6', borderRadius: '4px' }}>
        {error}
      </div>
    );
  }

  if (!quiz || questions.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
        <p>📝 Няма въпроси в този тест</p>
      </div>
    );
  }

  const score = calculateScore();
  const allAnswered = questions.every(q => answers[q.id]);

  return (
    <div style={{ padding: '20px' }}>
      <div style={{ marginBottom: '20px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h3>{quiz.title}</h3>
        <div style={{ fontSize: '0.9em', color: '#666' }}>
          {quiz.questions_count} въпроса • {quiz.total_points} точки
        </div>
      </div>

      {quiz.description && (
        <p style={{ marginBottom: '20px', color: '#666' }}>{quiz.description}</p>
      )}

      {showFinal && (
        <div style={{ 
          marginBottom: '20px', 
          padding: '15px', 
          backgroundColor: score.correct / score.total >= 0.7 ? '#e6ffe6' : '#ffe6e6',
          borderRadius: '4px',
          textAlign: 'center'
        }}>
          <h4>Резултат: {score.correct}/{score.total} ({Math.round((score.correct / score.total) * 100)}%)</h4>
          {score.correct / score.total >= 0.7 ? (
            <p style={{ color: 'green' }}>🎉 Отличен резултат!</p>
          ) : score.correct / score.total >= 0.5 ? (
            <p style={{ color: 'orange' }}>👍 Добър резултат!</p>
          ) : (
            <p style={{ color: 'red' }}>📚 Нужно е още учене.</p>
          )}
        </div>
      )}

      <div style={{ marginBottom: '20px' }}>
        {questions.map((question, index) => (
          <div key={question.id} style={{ 
            marginBottom: '25px', 
            padding: '20px', 
            border: '1px solid #ddd', 
            borderRadius: '8px',
            backgroundColor: '#f8f9fa'
          }}>
            <h4 style={{ marginBottom: '15px' }}>
              {index + 1}. {question.question_text}
            </h4>
            
            <div style={{ marginBottom: '15px' }}>
              {question.choices.map((choice, choiceIndex) => {
                const isSelected = answers[question.id] === choice;

                // Handle both letter format (A, B, C, D) and full text format
                let isCorrect = false;
                if (question.correct_choice === 'A' && choiceIndex === 0) isCorrect = true;
                else if (question.correct_choice === 'B' && choiceIndex === 1) isCorrect = true;
                else if (question.correct_choice === 'C' && choiceIndex === 2) isCorrect = true;
                else if (question.correct_choice === 'D' && choiceIndex === 3) isCorrect = true;
                else if (choice === question.correct_choice) isCorrect = true; // Fallback for full text

                const showResults = showResult[question.id];
                
                let backgroundColor = '#fff';
                let borderColor = '#ddd';
                let color = '#333';
                
                if (showResults) {
                  if (isCorrect) {
                    backgroundColor = '#e6ffe6';
                    borderColor = '#28a745';
                    color = '#28a745';
                  } else if (isSelected && !isCorrect) {
                    backgroundColor = '#ffe6e6';
                    borderColor = '#dc3545';
                    color = '#dc3545';
                  }
                } else if (isSelected) {
                  backgroundColor = '#e3f2fd';
                  borderColor = '#007bff';
                }

                return (
                  <div
                    key={choiceIndex}
                    onClick={() => !showResults && handleSelect(question.id, choice)}
                    style={{
                      padding: '10px 15px',
                      margin: '5px 0',
                      border: `2px solid ${borderColor}`,
                      borderRadius: '4px',
                      backgroundColor,
                      color,
                      cursor: showResults ? 'default' : 'pointer',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    <span style={{ 
                      marginRight: '10px', 
                      fontWeight: 'bold',
                      minWidth: '20px'
                    }}>
                      {LETTERS[choiceIndex]})
                    </span>
                    <span>{choice.replace(/^[A-D]\)\s*/, '')}</span>
                    {showResults && isCorrect && (
                      <span style={{ marginLeft: 'auto', color: '#28a745' }}>✓</span>
                    )}
                    {showResults && isSelected && !isCorrect && (
                      <span style={{ marginLeft: 'auto', color: '#dc3545' }}>✗</span>
                    )}
                  </div>
                );
              })}
            </div>

            {answers[question.id] && !showResult[question.id] && (
              <button
                onClick={() => handleCheck(question.id)}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#17a2b8',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  marginRight: '10px'
                }}
              >
                Провери отговора
              </button>
            )}

            {showResult[question.id] && question.explanation && (
              <div style={{ 
                marginTop: '15px', 
                padding: '10px', 
                backgroundColor: '#fff3cd', 
                borderRadius: '4px',
                borderLeft: '4px solid #ffc107'
              }}>
                <strong>Обяснение:</strong> {question.explanation}
              </div>
            )}
          </div>
        ))}
      </div>

      <div style={{ textAlign: 'center', marginTop: '30px' }}>
        {!showFinal && allAnswered && (
          <button
            onClick={handleFinish}
            style={{
              padding: '12px 24px',
              backgroundColor: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '16px',
              marginRight: '10px'
            }}
          >
            🏁 Завърши теста
          </button>
        )}
        
        {(showFinal || Object.keys(answers).length > 0) && (
          <button
            onClick={handleRestart}
            style={{
              padding: '12px 24px',
              backgroundColor: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            🔄 Започни отново
          </button>
        )}
      </div>

      {!allAnswered && (
        <div style={{ textAlign: 'center', marginTop: '20px', color: '#666' }}>
          Отговорени: {Object.keys(answers).length}/{questions.length} въпроса
        </div>
      )}
    </div>
  );
};

export default QuestionTest;
