import React, { useState, useEffect } from 'react';
import FlashcardView from './FlashcardView';
import QuestionTest from './QuestionTest';
import {
  generateFlashcards,
  generateQuiz,
  getStudySets,
  getFlashcards,
  type StudySet,
  type Flashcard
} from '../api/documents';

interface Props {
  documentId: string;
}

const DocumentTabs: React.FC<Props> = ({ documentId }) => {
  const [tab, setTab] = useState<'flashcards' | 'test'>('flashcards');
  const [studySets, setStudySets] = useState<StudySet[]>([]);
  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);
  const [flashcardStudySet, setFlashcardStudySet] = useState<StudySet | null>(null);
  const [quizStudySet, setQuizStudySet] = useState<StudySet | null>(null);
  const [flashLoading, setFlashLoading] = useState(true);
  const [flashError, setFlashError] = useState<string | null>(null);
  const [flashSuccess, setFlashSuccess] = useState<string | null>(null);
  const [flashGenLoading, setFlashGenLoading] = useState(false);
  const [quizGenLoading, setQuizGenLoading] = useState(false);
  const [flashIndex, setFlashIndex] = useState(0);

  const loadStudySets = async () => {
    try {
      const sets = await getStudySets(documentId);
      setStudySets(sets);

      // Find flashcard and quiz study sets
      const flashSet = sets.find(set => set.content_type === 'flashcards');
      const quizSet = sets.find(set => set.content_type === 'quiz');

      setFlashcardStudySet(flashSet || null);
      setQuizStudySet(quizSet || null);

      // Load flashcards if flashcard study set exists
      if (flashSet) {
        await loadFlashcards(flashSet.id);
      } else {
        setFlashLoading(false);
      }
    } catch (err) {
      console.error('Error loading study sets:', err);
      setFlashLoading(false);
    }
  };

  const loadFlashcards = async (studySetId: string) => {
    try {
      setFlashLoading(true);
      const cards = await getFlashcards(studySetId);
      setFlashcards(cards);
      setFlashIndex(0);
    } catch (err) {
      console.error('Error loading flashcards:', err);
      setFlashError('Грешка при зареждане на флашкарти');
    } finally {
      setFlashLoading(false);
    }
  };

  useEffect(() => {
    loadStudySets();
    // eslint-disable-next-line
  }, [documentId]);

  const handleGenerateFlashcards = async () => {
    setFlashGenLoading(true);
    setFlashError(null);
    setFlashSuccess(null);
    try {
      const result = await generateFlashcards(documentId);
      setFlashSuccess(`Генерирани ${result.flashcards_count} флашкарти успешно!`);
      // Reload study sets and flashcards
      await loadStudySets();
    } catch (err: any) {
      setFlashError(err.message || 'Грешка при генериране на флашкарти.');
    } finally {
      setFlashGenLoading(false);
    }
  };

  const handleGenerateQuiz = async () => {
    setQuizGenLoading(true);
    setFlashError(null);
    setFlashSuccess(null);
    try {
      const result = await generateQuiz(documentId);
      setFlashSuccess(`Генериран тест с ${result.questions_count} въпроса успешно!`);
      // Reload study sets
      await loadStudySets();
    } catch (err: any) {
      setFlashError(err.message || 'Грешка при генериране на тест.');
    } finally {
      setQuizGenLoading(false);
    }
  };

  return (
    <div style={{marginTop: 32}}>
      <div style={{display: 'flex', gap: 16, marginBottom: 16}}>
        <button
          onClick={() => setTab('flashcards')}
          style={{fontWeight: tab === 'flashcards' ? 'bold' : 'normal'}}
        >
          Флашкарти
        </button>
        <button
          onClick={() => setTab('test')}
          style={{fontWeight: tab === 'test' ? 'bold' : 'normal'}}
        >
          Тест
        </button>
      </div>
      <div>
        {tab === 'flashcards' && (
          <>
            <div style={{marginBottom: 16}}>
              <a
                href={`/api/documents/${documentId}/export/anki/`}
                target="_blank"
                rel="noopener noreferrer"
                style={{marginRight: 8, textDecoration: 'none', fontWeight: 'bold', color: '#1976d2'}}
              >
                Експортирай флашкартите (Anki)
              </a>
            </div>
            <FlashcardView
              flashcards={flashcards}
              loading={flashLoading}
              error={flashError}
              success={flashSuccess}
              genLoading={flashGenLoading}
              onGenerate={handleGenerateFlashcards}
              index={flashIndex}
              setIndex={setFlashIndex}
            />
          </>
        )}
        {tab === 'test' && (
          <>
            {quizStudySet && (
              <div style={{marginBottom: 16}}>
                <a
                  href={`/api/documents/${documentId}/export/pdf/`}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{marginRight: 8, textDecoration: 'none', fontWeight: 'bold', color: '#1976d2'}}
                >
                  Експортирай теста (PDF)
                </a>
                <a
                  href={`/api/documents/${documentId}/export/csv/`}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{textDecoration: 'none', fontWeight: 'bold', color: '#1976d2'}}
                >
                  Експортирай теста (CSV)
                </a>
              </div>
            )}

            {!quizStudySet ? (
              <div>
                <h3>Тест</h3>
                {flashError && <div style={{color: 'red'}}>{flashError}</div>}
                {flashSuccess && <div style={{color: 'green'}}>{flashSuccess}</div>}
                <div>Няма тест за този документ.</div>
                <button onClick={handleGenerateQuiz} disabled={quizGenLoading} style={{marginTop: 8}}>
                  {quizGenLoading ? 'Генериране...' : 'Генерирай тест'}
                </button>
              </div>
            ) : (
              <QuestionTest documentId={documentId} quizStudySet={quizStudySet} />
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default DocumentTabs; 