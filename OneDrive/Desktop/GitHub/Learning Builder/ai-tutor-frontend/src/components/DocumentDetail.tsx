import React, { useState } from 'react';
import FlashcardView from './FlashcardView';
import ExplanationList from './ExplanationList';
import QuestionTest from './QuestionTest';

interface Props {
  documentId: string;
  documentTitle?: string;
}

const DocumentDetail: React.FC<Props> = ({ documentId, documentTitle }) => {
  const [tab, setTab] = useState<'test' | 'flashcards' | 'explanations'>('test');

  return (
    <div style={{maxWidth: 700, margin: '0 auto', padding: 24, background: '#fff', borderRadius: 8, boxShadow: '0 2px 8px #eee'}}>
      {documentTitle && <h2 style={{marginBottom: 16}}>{documentTitle}</h2>}
      <div style={{display: 'flex', gap: 8, marginBottom: 24}}>
        <button onClick={() => setTab('test')} style={{background: tab==='test' ? '#1976d2' : '#eee', color: tab==='test' ? '#fff' : '#222', border: 'none', padding: '8px 16px', borderRadius: 4, cursor: 'pointer'}}>Тест</button>
        <button onClick={() => setTab('flashcards')} style={{background: tab==='flashcards' ? '#1976d2' : '#eee', color: tab==='flashcards' ? '#fff' : '#222', border: 'none', padding: '8px 16px', borderRadius: 4, cursor: 'pointer'}}>Флашкарти</button>
        <button onClick={() => setTab('explanations')} style={{background: tab==='explanations' ? '#1976d2' : '#eee', color: tab==='explanations' ? '#fff' : '#222', border: 'none', padding: '8px 16px', borderRadius: 4, cursor: 'pointer'}}>Обяснения</button>
      </div>
      <div>
        {tab === 'test' && <div><QuestionTest documentId={documentId} />{/* Тук ще има бутон за експорт на тест */}</div>}
        {tab === 'flashcards' && <div>Флашкарти функционалността е достъпна в DocumentTabs компонента{/* Тук ще има бутон за експорт на флашкарти */}</div>}
        {tab === 'explanations' && <div><ExplanationList documentId={documentId} />{/* Тук ще има бутон за експорт на обяснения */}</div>}
      </div>
    </div>
  );
};

export default DocumentDetail;