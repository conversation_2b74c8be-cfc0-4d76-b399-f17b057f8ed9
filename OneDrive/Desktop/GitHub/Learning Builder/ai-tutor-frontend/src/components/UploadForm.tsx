import React, { useState, useEffect, useRef } from 'react';
import { uploadDocument, getDocumentDetails, getCourses, type Course, type Document } from '../api/documents';

interface Props {
  onUpload: (document: Document) => void;
}

const UploadForm: React.FC<Props> = ({ onUpload }) => {
  const [file, setFile] = useState<File | null>(null);
  const [courses, setCourses] = useState<Course[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<string>('');
  const [document, setDocument] = useState<Document | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const intervalRef = useRef<number | null>(null);

  // Load courses on component mount
  useEffect(() => {
    const loadCourses = async () => {
      try {
        const coursesData = await getCourses();
        setCourses(coursesData);
      } catch (err) {
        console.error('Error loading courses:', err);
      }
    };
    loadCourses();
  }, []);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleCourseChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCourse(e.target.value);
  };

  const handleUpload = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setDocument(null);

    console.log('🎯 Upload form submitted');

    if (!file) {
      setError('Моля, избери файл.');
      return;
    }

    // Validate file
    const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'];
    if (!allowedTypes.includes(file.type) && !file.name.toLowerCase().match(/\.(pdf|docx|txt)$/)) {
      setError('Моля, изберете PDF, DOCX или TXT файл.');
      return;
    }

    if (file.size > 50 * 1024 * 1024) { // 50MB
      setError('Файлът е твърде голям. Максимален размер: 50MB.');
      return;
    }

    if (file.size < 100) { // 100 bytes minimum
      setError('Файлът е твърде малък.');
      return;
    }

    console.log('✅ File validation passed', {
      name: file.name,
      type: file.type,
      size: file.size,
      course: selectedCourse
    });

    setIsUploading(true);

    try {
      console.log('📤 Starting upload...');
      const uploadedDoc = await uploadDocument(file, selectedCourse || undefined);
      console.log('✅ Upload completed:', uploadedDoc);

      setDocument(uploadedDoc);
      onUpload(uploadedDoc);

      // Start polling for status if document is processing
      if (uploadedDoc.status === 'uploaded' || uploadedDoc.status === 'processing') {
        startStatusPolling(uploadedDoc.id);
      }
    } catch (err: any) {
      console.error('❌ Upload failed:', err);
      setError(err.message || 'Грешка при качване на файл.');
    } finally {
      setIsUploading(false);
    }
  };

  const startStatusPolling = (docId: string) => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(async () => {
      try {
        const updatedDoc = await getDocumentDetails(docId);
        setDocument(updatedDoc);
        onUpload(updatedDoc);

        // Stop polling if document is ready or failed
        if (updatedDoc.status === 'ready' || updatedDoc.status === 'failed') {
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
        }
      } catch (err) {
        console.error('Error checking document status:', err);
      }
    }, 3000);
  };

  // Cleanup interval on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready': return 'green';
      case 'failed': return 'red';
      case 'processing': return 'orange';
      case 'uploaded': return 'blue';
      default: return 'black';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'uploaded': return 'Качен';
      case 'processing': return 'Обработва се';
      case 'ready': return 'Готов';
      case 'failed': return 'Неуспешен';
      default: return status;
    }
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ddd', borderRadius: '8px' }}>
      <h3>Качване на документ</h3>

      <form onSubmit={handleUpload}>
        <div style={{ marginBottom: '15px' }}>
          <label htmlFor="file-input" style={{ display: 'block', marginBottom: '5px' }}>
            Избери файл (PDF, DOCX, TXT):
          </label>
          <input
            id="file-input"
            type="file"
            onChange={handleFileChange}
            accept=".pdf,.docx,.txt"
            disabled={isUploading}
          />
        </div>

        {courses.length > 0 && (
          <div style={{ marginBottom: '15px' }}>
            <label htmlFor="course-select" style={{ display: 'block', marginBottom: '5px' }}>
              Курс (опционално):
            </label>
            <select
              id="course-select"
              value={selectedCourse}
              onChange={handleCourseChange}
              disabled={isUploading}
              style={{ padding: '5px', minWidth: '200px' }}
            >
              <option value="">-- Без курс --</option>
              {courses.map(course => (
                <option key={course.id} value={course.id}>
                  {course.name}
                </option>
              ))}
            </select>
          </div>
        )}

        <button
          type="submit"
          disabled={!file || isUploading}
          style={{
            padding: '10px 20px',
            backgroundColor: isUploading ? '#ccc' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isUploading ? 'not-allowed' : 'pointer'
          }}
        >
          {isUploading ? 'Качва се...' : 'Качи документ'}
        </button>
      </form>

      {error && (
        <div style={{ color: 'red', marginTop: '10px', padding: '10px', backgroundColor: '#ffe6e6', borderRadius: '4px' }}>
          {error}
        </div>
      )}

      {document && (
        <div style={{ marginTop: '15px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
          <h4>Документ: {document.title}</h4>
          <p>
            Статус: <span style={{ color: getStatusColor(document.status), fontWeight: 'bold' }}>
              {getStatusText(document.status)}
            </span>
          </p>

          {document.course_name && (
            <p>Курс: {document.course_name}</p>
          )}

          <p>Размер: {document.file_size_mb} MB</p>

          {document.status === 'processing' && (
            <div style={{ color: 'orange' }}>
              ⏳ Документът се обработва... Моля изчакайте.
            </div>
          )}

          {document.status === 'ready' && (
            <div style={{ color: 'green' }}>
              ✅ Документът е обработен успешно! Можете да генерирате флашкарти и тестове.
            </div>
          )}

          {document.status === 'failed' && (
            <div style={{ color: 'red' }}>
              ❌ Възникна грешка при обработката на документа.
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default UploadForm;
