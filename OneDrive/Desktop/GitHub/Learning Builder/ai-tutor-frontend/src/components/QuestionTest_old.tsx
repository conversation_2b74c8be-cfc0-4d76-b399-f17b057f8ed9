import React, { useEffect, useState } from 'react';
import { getQuizDetails, type Quiz, type Question, type StudySet } from '../api/documents';

interface Props {
  documentId: string;
  quizStudySet?: StudySet;
}

const LETTERS = ['A', 'B', 'C', 'D'];

const QuestionTest: React.FC<Props> = ({ documentId, quizStudySet }) => {
  const [quiz, setQuiz] = useState<Quiz | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [answers, setAnswers] = useState<{ [id: string]: string }>({});
  const [showResult, setShowResult] = useState<{ [id: string]: boolean }>({});
  const [showFinal, setShowFinal] = useState(false);

  const loadQuiz = async () => {
    if (!quizStudySet) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      // Get the first quiz from the study set
      const quizData = await getQuizDetails(quizStudySet.id);
      setQuiz(quizData);
      setQuestions(quizData.questions);
    } catch (err) {
      console.error('Error loading quiz:', err);
      setError('Грешка при зареждане на теста');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadQuiz();
    // eslint-disable-next-line
  }, [quizStudySet]);

  const handleSelect = (qid: number, choice: string) => {
    setAnswers({ ...answers, [qid]: choice });
    setShowResult({ ...showResult, [qid]: true });
  };

  const handleRestart = () => {
    setAnswers({});
    setShowResult({});
    setShowFinal(false);
  };

  const allAnswered = questions.length > 0 && questions.every(q => answers[q.id]);

  // Изчисляване на резултата
  let correctCount = 0;
  questions.forEach(q => {
    const correctIndex = LETTERS.findIndex(l => l === q.correct_choice.trim());
    if (showFinal && answers[q.id] && q.choices[correctIndex] === answers[q.id]) {
      correctCount++;
    }
  });

  if (loading) return <div>Зареждане на тест...</div>;
  if (error) return <div style={{color: 'red'}}>{error}</div>;

  if (!questions.length) {
    return (
      <div>
        <h3>Тест с въпроси</h3>
        {success && <div style={{color: 'green'}}>{success}</div>}
        <div>Няма въпроси за този документ.</div>
        <button onClick={handleGenerate} disabled={genLoading} style={{marginTop: 8}}>
          {genLoading ? 'Генериране...' : 'Генерирай тест'}
        </button>
      </div>
    );
  }

  return (
    <div>
      <h3>Тест с въпроси</h3>
      {success && <div style={{color: 'green'}}>{success}</div>}
      {!showFinal && (
        <>
          <ul style={{listStyle: 'none', padding: 0}}>
            {questions.map((q, idx) => {
              const correctIndex = LETTERS.findIndex(l => l === q.correct_choice.trim());
              return (
                <li key={q.id} style={{marginBottom: 24, border: '1px solid #eee', borderRadius: 8, padding: 16}}>
                  <div style={{marginBottom: 8}}><b>{idx + 1}. {q.question_text}</b></div>
                  <div>
                    {q.choices.map((c, i) => (
                      <label key={i} style={{display: 'block', marginBottom: 4, cursor: showResult[q.id] ? 'default' : 'pointer'}}>
                        <input
                          type="radio"
                          name={`q_${q.id}`}
                          value={c}
                          disabled={!!showResult[q.id]}
                          checked={answers[q.id] === c}
                          onChange={() => handleSelect(q.id, c)}
                          style={{marginRight: 8}}
                        />
                        <b>{LETTERS[i]})</b> {c}
                        {showResult[q.id] && i === correctIndex && (
                          <span style={{color: 'green', marginLeft: 8}}>(Верен отговор)</span>
                        )}
                        {showResult[q.id] && answers[q.id] === c && i !== correctIndex && (
                          <span style={{color: 'red', marginLeft: 8}}>(Грешен отговор)</span>
                        )}
                      </label>
                    ))}
                  </div>
                  {showResult[q.id] && (
                    <div style={{marginTop: 8}}>
                      <b>Обяснение:</b> {q.explanation || 'Няма обяснение.'}
                    </div>
                  )}
                </li>
              );
            })}
          </ul>
          {allAnswered && (
            <button onClick={() => setShowFinal(true)} style={{marginTop: 16, fontWeight: 'bold'}}>Виж резултата</button>
          )}
        </>
      )}
      {showFinal && (
        <div style={{marginTop: 32, fontSize: 20, fontWeight: 'bold', color: '#1976d2'}}>
          Резултат: {correctCount} от {questions.length}
          <div>
            <button onClick={handleRestart} style={{marginTop: 16}}>Рестартирай теста</button>
          </div>
        </div>
      )}
    </div>
  );
};

export default QuestionTest; 