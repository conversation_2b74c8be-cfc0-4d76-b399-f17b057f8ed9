# 🔄 План за миграция към подобрена архитектура

## 📊 **Сравнение: Стара vs Нова структура**

### **Стара структура (проблеми):**
```
UploadedDocument
├── Questions (1:N) - всеки въпрос отделен ред
├── Flashcards (1:N) - всяка флашкарта отделен ред  
└── Explanations (1:N) - всяко обяснение отделен ред

❌ Няма user ownership
❌ Няма групиране в тестове
❌ Няма spaced repetition
❌ Няма статистики
❌ Няма курсове/организация
```

### **Нова структура (решения):**
```
User
├── UserProfile (1:1) - настройки и статистики
├── Courses (1:N) - организация по предмети
│   └── Documents (1:N) - документи в курс
│       └── StudySets (1:N) - колекции от материали
│           ├── Flashcards (1:N) - с spaced repetition
│           └── Quizzes (1:N) - тестове като цяло
│               └── Questions (1:N) - въпроси в тест
└── StudySessions (1:N) - сесии на учене
    ├── FlashcardReviews (1:N) - прегледи на флашкарти
    └── QuizAttempts (1:N) - опити за тестове

✅ Пълен user ownership
✅ Организация по курсове
✅ Spaced repetition алгоритъм
✅ Детайлни статистики
✅ Сесии на учене
✅ Прогрес tracking
```

## 🚀 **Стъпки за миграция**

### **Стъпка 1: Backup на данните**
```bash
# Backup на текущата база данни
python manage.py dumpdata > backup_old_data.json

# Backup на media файлове
cp -r media/ media_backup/
```

### **Стъпка 2: Създаване на нови модели**
```bash
# Преименуване на стария models.py
mv documents/models.py documents/models_old.py

# Използване на новия models.py
mv documents/models_improved.py documents/models.py

# Създаване на миграции
python manage.py makemigrations documents
```

### **Стъпка 3: Миграция на данните**
```python
# Скрипт за миграция на данните
def migrate_old_data():
    # 1. Създаване на default user ако няма
    # 2. Създаване на default course
    # 3. Миграция на документи
    # 4. Групиране на въпроси в quizzes
    # 5. Групиране на флашкарти в study sets
```

### **Стъпка 4: Обновяване на API**
- Нови serializers за новите модели
- Нови endpoints за курсове, сесии, статистики
- Spaced repetition API
- Прогрес tracking API

## 🎯 **Ключови подобрения**

### **1. User Management**
```python
# Всеки потребител има профил с настройки
user.profile.preferred_language = 'bg'
user.profile.ai_difficulty_level = 'medium'

# Статистики на потребителя
user.profile.total_documents_uploaded
user.profile.total_tests_taken
user.profile.total_study_time_minutes
```

### **2. Организация по курсове**
```python
# Потребителят създава курсове
course = Course.objects.create(
    owner=user,
    name="Математика 12 клас",
    description="Материали за матура",
    color="#e74c3c"
)

# Документите се организират в курсове
document = Document.objects.create(
    owner=user,
    course=course,
    title="Диференциално смятане"
)
```

### **3. Интелигентни Study Sets**
```python
# AI генерира study set с флашкарти
study_set = StudySet.objects.create(
    document=document,
    name="Диференциали - Флашкарти",
    content_type='flashcards',
    ai_prompt_used="Генерирай флашкарти за диференциално смятане"
)

# Флашкартите имат spaced repetition
flashcard = Flashcard.objects.create(
    study_set=study_set,
    front="Какво е производна?",
    back="Граница на отношението на приращенията",
    difficulty='medium',
    tags=['математика', 'анализ']
)
```

### **4. Тестове като цяло**
```python
# Тест се създава като единица
quiz = Quiz.objects.create(
    study_set=study_set,
    title="Тест по диференциали",
    time_limit_minutes=30,
    shuffle_questions=True
)

# Въпросите принадлежат на теста
question = Question.objects.create(
    quiz=quiz,
    question_text="Каква е производната на x²?",
    question_type='multiple_choice',
    choices=['A) 2x', 'B) x', 'C) 2', 'D) x²'],
    correct_choice='A) 2x',
    explanation="Производната на x² е 2x по правилото за степени"
)
```

### **5. Spaced Repetition**
```python
# Алгоритъм за оптимално повторение
def update_flashcard_schedule(flashcard, response_quality):
    if response_quality >= 3:  # Правилен отговор
        flashcard.interval_days *= flashcard.ease_factor
        flashcard.ease_factor += 0.1
    else:  # Грешен отговор
        flashcard.interval_days = 1
        flashcard.ease_factor = max(1.3, flashcard.ease_factor - 0.2)
    
    flashcard.next_review_date = timezone.now() + timedelta(days=flashcard.interval_days)
    flashcard.save()
```

### **6. Детайлни статистики**
```python
# Сесия на учене
session = StudySession.objects.create(
    user=user,
    study_set=study_set,
    session_type='flashcards'
)

# Преглед на флашкарта
review = FlashcardReview.objects.create(
    session=session,
    flashcard=flashcard,
    response_quality=3,  # Добре
    response_time_seconds=5
)

# Автоматично изчисляване на статистики
session.calculate_score()
```

## 📱 **Нови API Endpoints**

```python
# Курсове
GET /api/courses/ - списък курсове
POST /api/courses/ - създаване на курс
GET /api/courses/{id}/ - детайли за курс

# Study Sets
GET /api/study-sets/ - списък study sets
POST /api/study-sets/ - създаване на study set
GET /api/study-sets/{id}/flashcards/ - флашкарти в set

# Spaced Repetition
GET /api/flashcards/due/ - флашкарти за преглед днес
POST /api/flashcards/{id}/review/ - отбелязване на преглед

# Статистики
GET /api/stats/overview/ - общи статистики
GET /api/stats/progress/{course_id}/ - прогрес по курс
GET /api/stats/performance/ - performance данни

# Сесии
POST /api/sessions/start/ - започване на сесия
POST /api/sessions/{id}/end/ - приключване на сесия
GET /api/sessions/history/ - история на сесиите
```

## 🔧 **Технически подобрения**

### **1. Файлово съхранение**
```python
# За production - cloud storage
if settings.USE_CLOUD_STORAGE:
    DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
else:
    # Local storage с по-добра организация
    upload_to='documents/%Y/%m/%d/'
```

### **2. Кеширане на статистики**
```python
@cached_property
def user_stats(self):
    return {
        'total_study_time': self.study_sessions.aggregate(
            total=Sum('duration_minutes')
        )['total'] or 0,
        'average_score': self.study_sessions.aggregate(
            avg=Avg('score_percentage')
        )['avg'] or 0
    }
```

### **3. Background задачи**
```python
@shared_task
def update_spaced_repetition_schedule():
    """Ежедневно обновяване на spaced repetition графика."""
    due_flashcards = Flashcard.objects.filter(
        next_review_date__lte=timezone.now()
    )
    # Изпращане на напомняния
```

## 🎯 **Резултат**

След миграцията ще имаме:

✅ **Пълноценна LMS система** вместо прост document processor
✅ **Персонализирано учене** с spaced repetition
✅ **Детайлни статистики** и прогрес tracking
✅ **Организация по курсове** за по-добро управление
✅ **Scalable архитектура** готова за хиляди потребители
✅ **Modern UX** с интелигентни препоръки за учене
