"""
Подобрени serializers за новата структура на моделите.
"""
from rest_framework import serializers
from django.contrib.auth.models import User
from .models import (
    UserProfile, Course, Document, StudySet, 
    Quiz, Question, Flashcard, StudySession,
    FlashcardReview, QuizAttempt
)

class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer за потребителски профил."""
    username = serializers.CharField(source='user.username', read_only=True)
    email = serializers.EmailField(source='user.email', read_only=True)
    
    class Meta:
        model = UserProfile
        fields = [
            'username', 'email', 'preferred_language', 'ai_difficulty_level',
            'total_documents_uploaded', 'total_tests_taken', 'total_study_time_minutes',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'total_documents_uploaded', 'total_tests_taken', 'total_study_time_minutes',
            'created_at', 'updated_at'
        ]

class CourseSerializer(serializers.ModelSerializer):
    """Serializer за курсове."""
    documents_count = serializers.SerializerMethodField()
    study_sets_count = serializers.SerializerMethodField()
    last_activity = serializers.SerializerMethodField()
    
    class Meta:
        model = Course
        fields = [
            'id', 'name', 'description', 'color',
            'documents_count', 'study_sets_count', 'last_activity',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_documents_count(self, obj):
        return obj.documents.count()
    
    def get_study_sets_count(self, obj):
        return StudySet.objects.filter(document__course=obj).count()
    
    def get_last_activity(self, obj):
        latest_doc = obj.documents.order_by('-updated_at').first()
        return latest_doc.updated_at if latest_doc else obj.updated_at

class DocumentListSerializer(serializers.ModelSerializer):
    """Serializer за списък документи."""
    course_name = serializers.CharField(source='course.name', read_only=True)
    study_sets_count = serializers.SerializerMethodField()
    file_size_mb = serializers.SerializerMethodField()
    
    class Meta:
        model = Document
        fields = [
            'id', 'title', 'file_type', 'file_size_mb', 'status',
            'course_name', 'study_sets_count', 'view_count',
            'created_at', 'updated_at', 'last_accessed'
        ]
    
    def get_study_sets_count(self, obj):
        return obj.study_sets.count()
    
    def get_file_size_mb(self, obj):
        return round(obj.file_size / (1024 * 1024), 2)

class DocumentDetailSerializer(serializers.ModelSerializer):
    """Детайлен serializer за документ."""
    course = CourseSerializer(read_only=True)
    course_id = serializers.UUIDField(write_only=True, required=False)
    study_sets = serializers.SerializerMethodField()
    file_url = serializers.SerializerMethodField()
    
    class Meta:
        model = Document
        fields = [
            'id', 'title', 'file', 'file_url', 'file_size', 'file_type',
            'status', 'extracted_text', 'ai_summary',
            'course', 'course_id', 'study_sets',
            'view_count', 'study_sessions_count',
            'created_at', 'updated_at', 'last_accessed'
        ]
        read_only_fields = [
            'id', 'file_size', 'file_type', 'extracted_text', 'ai_summary',
            'view_count', 'study_sessions_count', 'created_at', 'updated_at'
        ]
    
    def get_file_url(self, obj):
        if obj.file:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.file.url)
        return None
    
    def get_study_sets(self, obj):
        study_sets = obj.study_sets.all()
        return StudySetListSerializer(study_sets, many=True).data

class StudySetListSerializer(serializers.ModelSerializer):
    """Serializer за списък study sets."""
    document_title = serializers.CharField(source='document.title', read_only=True)
    items_count = serializers.SerializerMethodField()
    
    class Meta:
        model = StudySet
        fields = [
            'id', 'name', 'description', 'content_type',
            'document_title', 'items_count', 'times_studied', 'average_score',
            'created_at', 'updated_at'
        ]
    
    def get_items_count(self, obj):
        if obj.content_type == 'flashcards':
            return obj.flashcards.count()
        elif obj.content_type == 'quiz':
            return sum(quiz.questions.count() for quiz in obj.quizzes.all())
        return 0

class FlashcardSerializer(serializers.ModelSerializer):
    """Serializer за флашкарти."""
    success_rate = serializers.ReadOnlyField()
    is_due = serializers.SerializerMethodField()
    
    class Meta:
        model = Flashcard
        fields = [
            'id', 'front', 'back', 'difficulty', 'tags',
            'ease_factor', 'interval_days', 'next_review_date',
            'times_reviewed', 'times_correct', 'success_rate', 'is_due',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'ease_factor', 'interval_days', 'next_review_date',
            'times_reviewed', 'times_correct', 'created_at', 'updated_at'
        ]
    
    def get_is_due(self, obj):
        from django.utils import timezone
        return obj.next_review_date <= timezone.now()

class QuestionSerializer(serializers.ModelSerializer):
    """Serializer за въпроси."""
    
    class Meta:
        model = Question
        fields = [
            'id', 'question_text', 'question_type', 'choices',
            'correct_choice', 'correct_answer', 'explanation',
            'points', 'order', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']

class QuizSerializer(serializers.ModelSerializer):
    """Serializer за тестове."""
    questions = QuestionSerializer(many=True, read_only=True)
    questions_count = serializers.SerializerMethodField()
    total_points = serializers.SerializerMethodField()
    
    class Meta:
        model = Quiz
        fields = [
            'id', 'title', 'description', 'time_limit_minutes',
            'shuffle_questions', 'show_correct_answers',
            'questions', 'questions_count', 'total_points',
            'times_taken', 'average_score',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'times_taken', 'average_score', 'created_at', 'updated_at'
        ]
    
    def get_questions_count(self, obj):
        return obj.questions.count()
    
    def get_total_points(self, obj):
        return sum(q.points for q in obj.questions.all())

class StudySetDetailSerializer(serializers.ModelSerializer):
    """Детайлен serializer за study set."""
    document = DocumentListSerializer(read_only=True)
    flashcards = FlashcardSerializer(many=True, read_only=True)
    quizzes = QuizSerializer(many=True, read_only=True)
    
    class Meta:
        model = StudySet
        fields = [
            'id', 'name', 'description', 'content_type',
            'ai_prompt_used', 'ai_model_version',
            'document', 'flashcards', 'quizzes',
            'times_studied', 'average_score',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'ai_prompt_used', 'ai_model_version',
            'times_studied', 'average_score', 'created_at', 'updated_at'
        ]

class StudySessionSerializer(serializers.ModelSerializer):
    """Serializer за сесии на учене."""
    study_set_name = serializers.CharField(source='study_set.name', read_only=True)
    duration_formatted = serializers.SerializerMethodField()
    
    class Meta:
        model = StudySession
        fields = [
            'id', 'session_type', 'study_set_name',
            'started_at', 'ended_at', 'duration_minutes', 'duration_formatted',
            'total_items', 'correct_answers', 'score_percentage',
            'completed', 'notes'
        ]
        read_only_fields = [
            'id', 'started_at', 'duration_minutes', 'score_percentage'
        ]
    
    def get_duration_formatted(self, obj):
        if obj.duration_minutes:
            hours = obj.duration_minutes // 60
            minutes = obj.duration_minutes % 60
            if hours > 0:
                return f"{hours}ч {minutes}мин"
            return f"{minutes}мин"
        return "N/A"

class FlashcardReviewSerializer(serializers.ModelSerializer):
    """Serializer за прегледи на флашкарти."""
    flashcard_front = serializers.CharField(source='flashcard.front', read_only=True)
    
    class Meta:
        model = FlashcardReview
        fields = [
            'id', 'flashcard_front', 'response_quality',
            'response_time_seconds', 'reviewed_at'
        ]
        read_only_fields = ['id', 'reviewed_at']

class QuizAttemptSerializer(serializers.ModelSerializer):
    """Serializer за опити за тестове."""
    quiz_title = serializers.CharField(source='quiz.title', read_only=True)
    score_percentage = serializers.SerializerMethodField()
    
    class Meta:
        model = QuizAttempt
        fields = [
            'id', 'quiz_title', 'answers', 'score', 'max_score',
            'score_percentage', 'started_at', 'submitted_at'
        ]
        read_only_fields = ['id', 'started_at']
    
    def get_score_percentage(self, obj):
        if obj.max_score and obj.max_score > 0:
            return round((obj.score / obj.max_score) * 100, 1)
        return 0

# Специални serializers за статистики
class UserStatsSerializer(serializers.Serializer):
    """Serializer за потребителски статистики."""
    total_documents = serializers.IntegerField()
    total_study_sets = serializers.IntegerField()
    total_flashcards = serializers.IntegerField()
    total_quizzes = serializers.IntegerField()
    total_study_time_minutes = serializers.IntegerField()
    average_quiz_score = serializers.FloatField()
    study_streak_days = serializers.IntegerField()
    last_study_date = serializers.DateTimeField()

class CourseStatsSerializer(serializers.Serializer):
    """Serializer за статистики по курс."""
    course_id = serializers.UUIDField()
    course_name = serializers.CharField()
    documents_count = serializers.IntegerField()
    study_sets_count = serializers.IntegerField()
    total_study_time_minutes = serializers.IntegerField()
    average_score = serializers.FloatField()
    last_activity = serializers.DateTimeField()

class SpacedRepetitionSerializer(serializers.Serializer):
    """Serializer за spaced repetition данни."""
    due_today = serializers.IntegerField()
    due_this_week = serializers.IntegerField()
    overdue = serializers.IntegerField()
    total_reviews_today = serializers.IntegerField()
    average_ease_factor = serializers.FloatField()
    success_rate_today = serializers.FloatField()
