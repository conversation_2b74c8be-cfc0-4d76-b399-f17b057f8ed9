"""
Management command за миграция от старите към новите модели.
"""
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.db import transaction
from django.utils import timezone
from documents.models_old import UploadedDocument as OldDocument
from documents.models_old import Question as OldQuestion
from documents.models_old import Flashcard as OldFlashcard
from documents.models_old import Explanation as OldExplanation
from documents.models import (
    UserProfile, Course, Document, StudySet, 
    Quiz, Question, Flashcard
)
import json

class Command(BaseCommand):
    help = 'Migrate data from old models to improved models structure'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be migrated without actually doing it'
        )
        parser.add_argument(
            '--default-user',
            type=str,
            default='admin',
            help='Username for default user (will be created if not exists)'
        )

    def handle(self, *args, **options):
        """
        Migrate data from old structure to new structure.
        """
        dry_run = options['dry_run']
        default_username = options['default_user']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        try:
            with transaction.atomic():
                # Step 1: Create or get default user
                user = self.get_or_create_default_user(default_username, dry_run)
                
                # Step 2: Create default course
                course = self.create_default_course(user, dry_run)
                
                # Step 3: Migrate documents
                document_mapping = self.migrate_documents(user, course, dry_run)
                
                # Step 4: Migrate flashcards
                self.migrate_flashcards(document_mapping, dry_run)
                
                # Step 5: Migrate questions into quizzes
                self.migrate_questions(document_mapping, dry_run)
                
                # Step 6: Create user profiles
                self.create_user_profiles(dry_run)
                
                if dry_run:
                    self.stdout.write(self.style.SUCCESS('DRY RUN completed successfully'))
                    # Rollback transaction in dry run
                    raise Exception("Dry run - rolling back")
                else:
                    self.stdout.write(self.style.SUCCESS('Migration completed successfully'))
                    
        except Exception as e:
            if not dry_run:
                self.stdout.write(self.style.ERROR(f'Migration failed: {e}'))
                raise
            else:
                self.stdout.write(self.style.SUCCESS('DRY RUN completed'))

    def get_or_create_default_user(self, username, dry_run):
        """Create or get default user."""
        try:
            user = User.objects.get(username=username)
            self.stdout.write(f'Using existing user: {username}')
        except User.DoesNotExist:
            if not dry_run:
                user = User.objects.create_user(
                    username=username,
                    email=f'{username}@example.com',
                    password='changeme123'
                )
                self.stdout.write(f'Created new user: {username}')
            else:
                self.stdout.write(f'Would create new user: {username}')
                user = User(username=username)  # Dummy for dry run
        
        return user

    def create_default_course(self, user, dry_run):
        """Create default course for migrated documents."""
        course_name = "Мигрирани документи"
        
        if not dry_run:
            course, created = Course.objects.get_or_create(
                owner=user,
                name=course_name,
                defaults={
                    'description': 'Автоматично създаден курс за мигрирани документи',
                    'color': '#3498db'
                }
            )
            if created:
                self.stdout.write(f'Created default course: {course_name}')
            else:
                self.stdout.write(f'Using existing course: {course_name}')
        else:
            self.stdout.write(f'Would create/use course: {course_name}')
            course = Course(name=course_name, owner=user)  # Dummy for dry run
        
        return course

    def migrate_documents(self, user, course, dry_run):
        """Migrate old documents to new structure."""
        try:
            old_documents = OldDocument.objects.all()
        except:
            self.stdout.write(self.style.WARNING('Old UploadedDocument model not found - skipping'))
            return {}
        
        document_mapping = {}
        
        for old_doc in old_documents:
            self.stdout.write(f'Migrating document: {old_doc.file.name}')
            
            if not dry_run:
                new_doc = Document.objects.create(
                    owner=user,
                    course=course,
                    title=old_doc.file.name.split('/')[-1],  # Extract filename
                    file=old_doc.file,
                    file_size=old_doc.file.size if old_doc.file else 0,
                    file_type=self.detect_file_type(old_doc.file.name),
                    status=old_doc.status,
                    extracted_text=getattr(old_doc, 'extracted_text', ''),
                    ai_summary=getattr(old_doc, 'summary', ''),
                    created_at=old_doc.created_at,
                    updated_at=old_doc.updated_at,
                )
                document_mapping[old_doc.id] = new_doc
            else:
                self.stdout.write(f'  Would create document: {old_doc.file.name}')
                document_mapping[old_doc.id] = None
        
        self.stdout.write(f'Migrated {len(old_documents)} documents')
        return document_mapping

    def migrate_flashcards(self, document_mapping, dry_run):
        """Migrate old flashcards to new structure."""
        try:
            old_flashcards = OldFlashcard.objects.all()
        except:
            self.stdout.write(self.style.WARNING('Old Flashcard model not found - skipping'))
            return
        
        # Group flashcards by document
        flashcards_by_doc = {}
        for flashcard in old_flashcards:
            doc_id = flashcard.document_id
            if doc_id not in flashcards_by_doc:
                flashcards_by_doc[doc_id] = []
            flashcards_by_doc[doc_id].append(flashcard)
        
        for doc_id, flashcards in flashcards_by_doc.items():
            if doc_id not in document_mapping:
                continue
                
            new_doc = document_mapping[doc_id]
            if new_doc is None:  # Dry run
                self.stdout.write(f'  Would create flashcard study set for document {doc_id}')
                continue
            
            # Create study set for flashcards
            study_set = StudySet.objects.create(
                document=new_doc,
                name=f"Флашкарти - {new_doc.title}",
                content_type='flashcards',
                ai_prompt_used="Мигрирано от стара система"
            )
            
            # Migrate flashcards
            for old_flashcard in flashcards:
                if not dry_run:
                    Flashcard.objects.create(
                        study_set=study_set,
                        front=old_flashcard.front,
                        back=old_flashcard.back,
                        created_at=old_flashcard.created_at,
                        updated_at=old_flashcard.updated_at,
                    )
            
            self.stdout.write(f'  Migrated {len(flashcards)} flashcards for document {new_doc.title}')
        
        total_flashcards = sum(len(cards) for cards in flashcards_by_doc.values())
        self.stdout.write(f'Migrated {total_flashcards} flashcards total')

    def migrate_questions(self, document_mapping, dry_run):
        """Migrate old questions to new quiz structure."""
        try:
            old_questions = OldQuestion.objects.all()
        except:
            self.stdout.write(self.style.WARNING('Old Question model not found - skipping'))
            return
        
        # Group questions by document
        questions_by_doc = {}
        for question in old_questions:
            doc_id = question.document_id
            if doc_id not in questions_by_doc:
                questions_by_doc[doc_id] = []
            questions_by_doc[doc_id].append(question)
        
        for doc_id, questions in questions_by_doc.items():
            if doc_id not in document_mapping:
                continue
                
            new_doc = document_mapping[doc_id]
            if new_doc is None:  # Dry run
                self.stdout.write(f'  Would create quiz for document {doc_id}')
                continue
            
            # Create study set for quiz
            study_set = StudySet.objects.create(
                document=new_doc,
                name=f"Тест - {new_doc.title}",
                content_type='quiz',
                ai_prompt_used="Мигрирано от стара система"
            )
            
            # Create quiz
            quiz = Quiz.objects.create(
                study_set=study_set,
                title=f"Тест по {new_doc.title}",
                description="Автоматично мигриран тест",
                shuffle_questions=True,
                show_correct_answers=True
            )
            
            # Migrate questions
            for i, old_question in enumerate(questions):
                if not dry_run:
                    Question.objects.create(
                        quiz=quiz,
                        question_text=old_question.question_text,
                        question_type='multiple_choice',
                        choices=old_question.choices if hasattr(old_question, 'choices') else [],
                        correct_choice=old_question.correct_choice if hasattr(old_question, 'correct_choice') else '',
                        explanation=old_question.explanation if hasattr(old_question, 'explanation') else '',
                        order=i,
                        created_at=old_question.created_at,
                    )
            
            self.stdout.write(f'  Migrated {len(questions)} questions for document {new_doc.title}')
        
        total_questions = sum(len(qs) for qs in questions_by_doc.values())
        self.stdout.write(f'Migrated {total_questions} questions total')

    def create_user_profiles(self, dry_run):
        """Create user profiles for all users."""
        users_without_profile = User.objects.filter(profile__isnull=True)
        
        for user in users_without_profile:
            if not dry_run:
                UserProfile.objects.create(
                    user=user,
                    total_documents_uploaded=Document.objects.filter(owner=user).count()
                )
                self.stdout.write(f'Created profile for user: {user.username}')
            else:
                self.stdout.write(f'Would create profile for user: {user.username}')

    def detect_file_type(self, filename):
        """Detect file type from filename."""
        if filename.lower().endswith('.pdf'):
            return 'pdf'
        elif filename.lower().endswith('.docx'):
            return 'docx'
        elif filename.lower().endswith('.txt'):
            return 'txt'
        else:
            return 'txt'  # Default
