"""
Management command for system health checks and monitoring.
"""
import time
import json
from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.db import connection
from django.conf import settings
from documents.models import UploadedDocument, Question, Flashcard, Explanation
from documents.utils.cache import get_cache_stats
from documents.ai.client import OpenAIClient

class Command(BaseCommand):
    help = 'Perform system health checks and display status'

    def add_arguments(self, parser):
        parser.add_argument(
            '--format',
            choices=['text', 'json'],
            default='text',
            help='Output format (text or json)'
        )
        parser.add_argument(
            '--check-ai',
            action='store_true',
            help='Include AI service health check'
        )

    def handle(self, *args, **options):
        """
        Perform comprehensive system health checks.
        """
        health_data = {
            'timestamp': time.time(),
            'status': 'healthy',
            'checks': {}
        }

        # Database health check
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                health_data['checks']['database'] = {
                    'status': 'healthy',
                    'response_time_ms': self._measure_db_response_time()
                }
        except Exception as e:
            health_data['checks']['database'] = {
                'status': 'unhealthy',
                'error': str(e)
            }
            health_data['status'] = 'unhealthy'

        # Cache health check
        try:
            test_key = 'health_check_test'
            test_value = 'test_value'
            cache.set(test_key, test_value, 60)
            retrieved_value = cache.get(test_key)
            
            if retrieved_value == test_value:
                health_data['checks']['cache'] = {
                    'status': 'healthy',
                    'stats': get_cache_stats()
                }
            else:
                health_data['checks']['cache'] = {
                    'status': 'unhealthy',
                    'error': 'Cache read/write test failed'
                }
                health_data['status'] = 'unhealthy'
                
            cache.delete(test_key)
        except Exception as e:
            health_data['checks']['cache'] = {
                'status': 'unhealthy',
                'error': str(e)
            }
            health_data['status'] = 'unhealthy'

        # Data statistics
        try:
            health_data['checks']['data_stats'] = {
                'status': 'healthy',
                'documents_count': UploadedDocument.objects.count(),
                'questions_count': Question.objects.count(),
                'flashcards_count': Flashcard.objects.count(),
                'explanations_count': Explanation.objects.count(),
                'ready_documents': UploadedDocument.objects.filter(status='ready').count(),
                'processing_documents': UploadedDocument.objects.filter(status='processing').count(),
                'failed_documents': UploadedDocument.objects.filter(status='failed').count(),
            }
        except Exception as e:
            health_data['checks']['data_stats'] = {
                'status': 'unhealthy',
                'error': str(e)
            }
            health_data['status'] = 'unhealthy'

        # AI service health check (optional)
        if options['check_ai']:
            try:
                client = OpenAIClient()
                start_time = time.time()
                response = client.generate_completion(
                    "Respond with 'OK' if you can process this request.",
                    use_cache=False
                )
                response_time = (time.time() - start_time) * 1000
                
                if 'OK' in response or 'ok' in response.lower():
                    health_data['checks']['ai_service'] = {
                        'status': 'healthy',
                        'response_time_ms': round(response_time, 2)
                    }
                else:
                    health_data['checks']['ai_service'] = {
                        'status': 'degraded',
                        'response_time_ms': round(response_time, 2),
                        'warning': 'Unexpected AI response'
                    }
            except Exception as e:
                health_data['checks']['ai_service'] = {
                    'status': 'unhealthy',
                    'error': str(e)
                }
                health_data['status'] = 'unhealthy'

        # Output results
        if options['format'] == 'json':
            self.stdout.write(json.dumps(health_data, indent=2))
        else:
            self._print_text_output(health_data)

    def _measure_db_response_time(self):
        """Measure database response time."""
        start_time = time.time()
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM documents_uploadeddocument")
            cursor.fetchone()
        return round((time.time() - start_time) * 1000, 2)

    def _print_text_output(self, health_data):
        """Print health data in human-readable format."""
        status_color = self.style.SUCCESS if health_data['status'] == 'healthy' else self.style.ERROR
        
        self.stdout.write(status_color(f"System Status: {health_data['status'].upper()}"))
        self.stdout.write(f"Timestamp: {time.ctime(health_data['timestamp'])}")
        self.stdout.write("")

        for check_name, check_data in health_data['checks'].items():
            status = check_data['status']
            if status == 'healthy':
                status_style = self.style.SUCCESS
            elif status == 'degraded':
                status_style = self.style.WARNING
            else:
                status_style = self.style.ERROR

            self.stdout.write(f"{check_name.replace('_', ' ').title()}:")
            self.stdout.write(f"  Status: {status_style(status)}")
            
            if 'error' in check_data:
                self.stdout.write(f"  Error: {self.style.ERROR(check_data['error'])}")
            
            if 'response_time_ms' in check_data:
                self.stdout.write(f"  Response Time: {check_data['response_time_ms']}ms")
            
            if 'warning' in check_data:
                self.stdout.write(f"  Warning: {self.style.WARNING(check_data['warning'])}")
            
            if check_name == 'data_stats' and status == 'healthy':
                stats = check_data
                self.stdout.write(f"  Documents: {stats['documents_count']} total")
                self.stdout.write(f"    Ready: {stats['ready_documents']}")
                self.stdout.write(f"    Processing: {stats['processing_documents']}")
                self.stdout.write(f"    Failed: {stats['failed_documents']}")
                self.stdout.write(f"  Questions: {stats['questions_count']}")
                self.stdout.write(f"  Flashcards: {stats['flashcards_count']}")
                self.stdout.write(f"  Explanations: {stats['explanations_count']}")
            
            if check_name == 'cache' and 'stats' in check_data:
                cache_stats = check_data['stats']
                if 'error' not in cache_stats:
                    self.stdout.write(f"  Memory Used: {cache_stats.get('used_memory', 'N/A')}")
                    self.stdout.write(f"  Connected Clients: {cache_stats.get('connected_clients', 'N/A')}")
            
            self.stdout.write("")
