"""
Management command for cache management operations.
"""
from django.core.management.base import BaseCommand
from django.core.cache import cache
from documents.utils.cache import AIResultCache

class Command(BaseCommand):
    help = 'Clear application cache'

    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            choices=['all', 'ai', 'ratelimit'],
            default='all',
            help='Type of cache to clear (all, ai, ratelimit)'
        )
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Skip confirmation prompt'
        )

    def handle(self, *args, **options):
        """
        Clear specified cache types.
        """
        cache_type = options['type']
        
        if not options['confirm']:
            confirm = input(f"Are you sure you want to clear {cache_type} cache? (y/N): ")
            if confirm.lower() != 'y':
                self.stdout.write(self.style.WARNING('Cache clear cancelled.'))
                return

        if cache_type == 'all':
            cache.clear()
            self.stdout.write(self.style.SUCCESS('All cache cleared successfully.'))
        elif cache_type == 'ai':
            AIResultCache.clear_all_cache()
            self.stdout.write(self.style.SUCCESS('AI result cache cleared successfully.'))
        elif cache_type == 'ratelimit':
            # Clear rate limiting cache keys
            # This is a simplified approach - in production you might want more sophisticated key management
            cache.clear()
            self.stdout.write(self.style.SUCCESS('Rate limiting cache cleared successfully.'))

        self.stdout.write('Cache clear operation completed.')
