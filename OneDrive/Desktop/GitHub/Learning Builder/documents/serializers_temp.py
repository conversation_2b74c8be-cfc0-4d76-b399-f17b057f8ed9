"""
Временни serializers за миграция.
"""
from rest_framework import serializers
from django.core.exceptions import ValidationError

def validate_file_extension(value):
    """
    Валидира, че файлът е с разширение pdf, docx или txt.
    """
    allowed = ('.pdf', '.docx', '.txt')
    if not value.name.lower().endswith(allowed):
        raise ValidationError("Unsupported file type. Позволени са само PDF, DOCX, TXT.")

def validate_file_size(value):
    """
    Валидира размера на файла - максимум 50MB.
    """
    max_size = 50 * 1024 * 1024  # 50MB в байтове
    if value.size > max_size:
        raise ValidationError(f"Файлът е твърде голям. Максимален размер: 50MB. Вашият файл: {value.size / (1024*1024):.1f}MB")

def validate_file_content(value):
    """
    Основна валидация на съдържанието на файла.
    """
    # Проверка за минимален размер
    min_size = 100  # 100 байта минимум
    if value.size < min_size:
        raise ValidationError("Файлът е твърде малък за обработка.")
    
    # Проверка за подозрителни файлове
    dangerous_extensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com']
    file_name_lower = value.name.lower()
    for ext in dangerous_extensions:
        if ext in file_name_lower:
            raise ValidationError("Този тип файл не е разрешен от съображения за сигурност.")
    
    # Проверка за валидно име на файл
    if len(value.name) > 255:
        raise ValidationError("Името на файла е твърде дълго.")
    
    # Проверка за валидни символи в името
    import re
    if not re.match(r'^[a-zA-Z0-9._\-\s\u0400-\u04FF]+$', value.name):
        raise ValidationError("Името на файла съдържа невалидни символи.")

# Временен празен serializer
class TempSerializer(serializers.Serializer):
    pass
