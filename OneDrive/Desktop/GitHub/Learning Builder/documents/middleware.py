"""
Custom middleware for logging and monitoring.
"""
import time
import logging
import json
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from django_ratelimit.exceptions import Ratelimited

logger = logging.getLogger(__name__)

class RequestLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log all requests with timing and response information.
    """
    
    def process_request(self, request):
        """
        Log incoming request details.
        """
        request.start_time = time.time()
        
        # Log request details
        log_data = {
            'event': 'request_start',
            'method': request.method,
            'path': request.path,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'ip_address': self.get_client_ip(request),
            'content_length': request.META.get('CONTENT_LENGTH', 0),
        }
        
        # Log POST data size for file uploads
        if request.method == 'POST' and hasattr(request, 'FILES'):
            total_size = sum(f.size for f in request.FILES.values())
            log_data['upload_size'] = total_size
        
        logger.info(json.dumps(log_data))
    
    def process_response(self, request, response):
        """
        Log response details and request duration.
        """
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            
            log_data = {
                'event': 'request_complete',
                'method': request.method,
                'path': request.path,
                'status_code': response.status_code,
                'duration_ms': round(duration * 1000, 2),
                'response_size': len(response.content) if hasattr(response, 'content') else 0,
                'ip_address': self.get_client_ip(request),
            }
            
            # Log level based on status code
            if response.status_code >= 500:
                logger.error(json.dumps(log_data))
            elif response.status_code >= 400:
                logger.warning(json.dumps(log_data))
            else:
                logger.info(json.dumps(log_data))
        
        return response
    
    def process_exception(self, request, exception):
        """
        Log exceptions that occur during request processing.
        """
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
        else:
            duration = 0
        
        log_data = {
            'event': 'request_exception',
            'method': request.method,
            'path': request.path,
            'exception_type': type(exception).__name__,
            'exception_message': str(exception),
            'duration_ms': round(duration * 1000, 2),
            'ip_address': self.get_client_ip(request),
        }
        
        logger.error(json.dumps(log_data))
        
        # Handle rate limiting exceptions
        if isinstance(exception, Ratelimited):
            return JsonResponse({
                'error': 'Rate limit exceeded',
                'message': 'Твърде много заявки. Моля опитайте отново по-късно.',
                'retry_after': 3600  # 1 hour
            }, status=429)
        
        return None
    
    @staticmethod
    def get_client_ip(request):
        """
        Get the client's IP address from the request.
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    Middleware to add security headers to responses.
    """
    
    def process_response(self, request, response):
        """
        Add security headers to the response.
        """
        # Content Security Policy
        response['Content-Security-Policy'] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data:; "
            "font-src 'self'; "
            "connect-src 'self';"
        )
        
        # Other security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # HSTS (only for HTTPS)
        if request.is_secure():
            response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        return response

class HealthCheckMiddleware(MiddlewareMixin):
    """
    Middleware to handle health check requests without full processing.
    """
    
    def process_request(self, request):
        """
        Handle health check requests quickly.
        """
        if request.path in ['/health/', '/health', '/ping/', '/ping']:
            return JsonResponse({
                'status': 'healthy',
                'timestamp': time.time(),
                'service': 'ai-tutor-platform'
            })
        
        return None
