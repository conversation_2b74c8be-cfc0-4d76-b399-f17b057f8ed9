"""
Подобрени views за новата структура на моделите.
"""
from django.shortcuts import get_object_or_404
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, generics
from rest_framework.decorators import api_view
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator
from django.contrib.auth.models import User
from django.db.models import Q, Count, Avg
from django.utils import timezone

from .models import (
    UserProfile, Course, Document, StudySet, 
    Quiz, Question, Flashcard, StudySession
)
from .serializers import (
    UserProfileSerializer, CourseSerializer, DocumentSerializer,
    StudySetSerializer, QuizSerializer, QuestionSerializer,
    FlashcardSerializer, StudySessionSerializer
)

# User Profile Views
class UserProfileView(generics.RetrieveUpdateAPIView):
    """Преглед и редактиране на потребителски профил."""
    serializer_class = UserProfileSerializer
    
    def get_object(self):
        profile, created = UserProfile.objects.get_or_create(user=self.request.user)
        return profile

# Course Views
class CourseListCreateView(generics.ListCreateAPIView):
    """Списък и създаване на курсове."""
    serializer_class = CourseSerializer
    
    def get_queryset(self):
        return Course.objects.filter(owner=self.request.user)
    
    def perform_create(self, serializer):
        serializer.save(owner=self.request.user)

class CourseDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Детайли, редактиране и изтриване на курс."""
    serializer_class = CourseSerializer
    
    def get_queryset(self):
        return Course.objects.filter(owner=self.request.user)

# Document Views
@method_decorator(ratelimit(key='ip', rate='10/h', method='POST', block=True), name='post')
class DocumentUploadView(generics.CreateAPIView):
    """Качване на документи."""
    serializer_class = DocumentSerializer
    
    def perform_create(self, serializer):
        serializer.save(owner=self.request.user, status='uploaded')

class DocumentListView(generics.ListAPIView):
    """Списък документи."""
    serializer_class = DocumentSerializer
    
    def get_queryset(self):
        queryset = Document.objects.filter(owner=self.request.user)
        
        # Филтриране по курс
        course_id = self.request.query_params.get('course', None)
        if course_id:
            queryset = queryset.filter(course_id=course_id)
        
        # Филтриране по статус
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Търсене
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) | 
                Q(extracted_text__icontains=search)
            )
        
        return queryset.order_by('-updated_at')

class DocumentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Детайли, редактиране и изтриване на документ."""
    serializer_class = DocumentSerializer
    
    def get_queryset(self):
        return Document.objects.filter(owner=self.request.user)
    
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.mark_accessed()  # Отбелязваме достъпа
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

# Study Set Views
class StudySetListView(generics.ListAPIView):
    """Списък study sets."""
    serializer_class = StudySetSerializer
    
    def get_queryset(self):
        queryset = StudySet.objects.filter(document__owner=self.request.user)
        
        # Филтриране по документ
        document_id = self.request.query_params.get('document', None)
        if document_id:
            queryset = queryset.filter(document_id=document_id)
        
        # Филтриране по тип съдържание
        content_type = self.request.query_params.get('content_type', None)
        if content_type:
            queryset = queryset.filter(content_type=content_type)
        
        return queryset.order_by('-created_at')

class StudySetDetailView(generics.RetrieveAPIView):
    """Детайли за study set."""
    serializer_class = StudySetSerializer
    
    def get_queryset(self):
        return StudySet.objects.filter(document__owner=self.request.user)

# AI Generation Views
@method_decorator(ratelimit(key='ip', rate='5/h', method='POST', block=True), name='post')
class GenerateFlashcardsView(APIView):
    """Генериране на флашкарти от документ."""
    
    def post(self, request, document_id):
        try:
            from .ai.client import OpenAIClient
            from .ai.prompts import generate_flashcards_prompt
            from .tasks import parse_flashcards
            from documents.utils.text_extraction import extract_text
            
            # Намиране на документа
            document = get_object_or_404(Document, id=document_id, owner=request.user)
            
            # Извличане на текст
            extracted_text = extract_text(document.file.path)
            if not extracted_text or len(extracted_text.strip()) < 100:
                return Response({
                    'error': 'Документът е твърде кратък или не може да бъде обработен'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # AI генериране
            client = OpenAIClient()
            prompt = generate_flashcards_prompt(extracted_text)
            ai_response = client.generate_completion(prompt, content_type='flashcards')
            
            # Парсване на резултата
            parsed_flashcards = parse_flashcards(ai_response)
            
            if not parsed_flashcards:
                return Response({
                    'error': 'AI не успя да генерира валидни флашкарти'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            # Създаване на study set
            study_set = StudySet.objects.create(
                document=document,
                name=f"Флашкарти - {document.title}",
                content_type='flashcards',
                ai_prompt_used=prompt,
                ai_model_version='gpt-3.5-turbo'
            )
            
            # Създаване на флашкарти
            flashcards_created = []
            for flashcard_data in parsed_flashcards:
                flashcard = Flashcard.objects.create(
                    study_set=study_set,
                    front=flashcard_data['front'],
                    back=flashcard_data['back']
                )
                flashcards_created.append(flashcard)
            
            # Обновяване на статуса на документа
            document.status = 'ready'
            document.save()
            
            return Response({
                'study_set_id': study_set.id,
                'flashcards_count': len(flashcards_created),
                'status': 'success'
            })
            
        except Document.DoesNotExist:
            return Response({
                'error': 'Документът не е намерен'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': f'Грешка при генериране на флашкарти: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@method_decorator(ratelimit(key='ip', rate='5/h', method='POST', block=True), name='post')
class GenerateQuizView(APIView):
    """Генериране на тест от документ."""
    
    def post(self, request, document_id):
        try:
            from .ai.client import OpenAIClient
            from .ai.prompts import generate_questions_prompt
            from .tasks import parse_questions
            from documents.utils.text_extraction import extract_text
            
            # Намиране на документа
            document = get_object_or_404(Document, id=document_id, owner=request.user)
            
            # Извличане на текст
            extracted_text = extract_text(document.file.path)
            if not extracted_text or len(extracted_text.strip()) < 100:
                return Response({
                    'error': 'Документът е твърде кратък или не може да бъде обработен'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # AI генериране
            client = OpenAIClient()
            prompt = generate_questions_prompt(extracted_text)
            ai_response = client.generate_completion(prompt, content_type='questions')
            
            # Парсване на резултата
            parsed_questions = parse_questions(ai_response)
            
            if not parsed_questions:
                return Response({
                    'error': 'AI не успя да генерира валидни въпроси'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            # Създаване на study set
            study_set = StudySet.objects.create(
                document=document,
                name=f"Тест - {document.title}",
                content_type='quiz',
                ai_prompt_used=prompt,
                ai_model_version='gpt-3.5-turbo'
            )
            
            # Създаване на quiz
            quiz = Quiz.objects.create(
                study_set=study_set,
                title=f"Тест по {document.title}",
                description=f"Автоматично генериран тест от документа '{document.title}'",
                shuffle_questions=True,
                show_correct_answers=True
            )
            
            # Създаване на въпроси
            questions_created = []
            for i, question_data in enumerate(parsed_questions):
                question = Question.objects.create(
                    quiz=quiz,
                    question_text=question_data['question_text'],
                    question_type='multiple_choice',
                    choices=question_data['choices'],
                    correct_choice=question_data['correct_choice'],
                    explanation=question_data.get('explanation', ''),
                    order=i,
                    points=1
                )
                questions_created.append(question)
            
            # Обновяване на статуса на документа
            document.status = 'ready'
            document.save()
            
            return Response({
                'study_set_id': study_set.id,
                'quiz_id': quiz.id,
                'questions_count': len(questions_created),
                'status': 'success'
            })
            
        except Document.DoesNotExist:
            return Response({
                'error': 'Документът не е намерен'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': f'Грешка при генериране на тест: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Flashcard Views
class FlashcardListView(generics.ListAPIView):
    """Списък флашкарти."""
    serializer_class = FlashcardSerializer
    
    def get_queryset(self):
        study_set_id = self.kwargs.get('study_set_id')
        return Flashcard.objects.filter(
            study_set_id=study_set_id,
            study_set__document__owner=self.request.user
        ).order_by('next_review_date')

class FlashcardsDueView(generics.ListAPIView):
    """Флашкарти за преглед днес."""
    serializer_class = FlashcardSerializer
    
    def get_queryset(self):
        return Flashcard.objects.filter(
            study_set__document__owner=self.request.user,
            next_review_date__lte=timezone.now()
        ).order_by('next_review_date')

# Quiz Views
class QuizDetailView(generics.RetrieveAPIView):
    """Детайли за тест."""
    serializer_class = QuizSerializer
    
    def get_queryset(self):
        return Quiz.objects.filter(study_set__document__owner=self.request.user)

# Statistics Views
@api_view(['GET'])
def user_statistics(request):
    """Статистики за потребителя."""
    user = request.user
    
    stats = {
        'total_documents': Document.objects.filter(owner=user).count(),
        'total_study_sets': StudySet.objects.filter(document__owner=user).count(),
        'total_flashcards': Flashcard.objects.filter(study_set__document__owner=user).count(),
        'total_quizzes': Quiz.objects.filter(study_set__document__owner=user).count(),
        'total_study_sessions': StudySession.objects.filter(user=user).count(),
        'average_session_score': StudySession.objects.filter(
            user=user, 
            score_percentage__isnull=False
        ).aggregate(avg_score=Avg('score_percentage'))['avg_score'] or 0,
        'documents_by_status': Document.objects.filter(owner=user).values('status').annotate(
            count=Count('id')
        ),
        'recent_activity': Document.objects.filter(owner=user).order_by('-updated_at')[:5].values(
            'id', 'title', 'updated_at', 'status'
        )
    }
    
    return Response(stats)
