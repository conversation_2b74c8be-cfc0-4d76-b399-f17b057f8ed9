from celery import shared_task
from django.core.files.storage import default_storage
from .models import Document, Question, Flashcard, StudySet, Quiz
from documents.utils.text_extraction import extract_text
from documents.ai.prompts import generate_summary_prompt, generate_flashcards_prompt, generate_questions_prompt, generate_explanations_prompt
from documents.ai.client import OpenAIClient, call_openai
import re
import json
import logging

def parse_questions(ai_output: str):
    """
    Робустен парсер за въпроси с множество fallback стратегии.
    Поддържа различни формати и автоматично възстановяване при грешки.
    """
    import json
    import logging

    logger = logging.getLogger(__name__)
    logger.info(f'Parsing AI questions output: {ai_output[:200]}...')

    questions = []

    # Стратегия 1: Опит за JSON парсване
    try:
        if ai_output.strip().startswith('[') or ai_output.strip().startswith('{'):
            json_data = json.loads(ai_output)
            if isinstance(json_data, list):
                for item in json_data:
                    if all(key in item for key in ['question_text', 'choices', 'correct_choice']):
                        questions.append({
                            'question_text': str(item.get('question_text', '')).strip(),
                            'choices': [str(c).strip() for c in item.get('choices', [])],
                            'correct_choice': str(item.get('correct_choice', '')).strip(),
                            'explanation': str(item.get('explanation', '')).strip()
                        })
                if questions:
                    logger.info(f'Successfully parsed {len(questions)} questions from JSON')
                    return questions
    except (json.JSONDecodeError, KeyError, TypeError) as e:
        logger.warning(f'JSON parsing failed: {e}')

    # Стратегия 2: Парсване по разделители (оригинален формат)
    for line in ai_output.strip().split('\n'):
        line = line.strip()
        if not line or line.startswith('#') or len(line) < 10:
            continue

        # Опит за парсване по формат с |
        parts = [p.strip() for p in line.split('|')]
        if len(parts) >= 6:
            question_text = parts[0]
            choices = []

            # Извличане на избори
            for i in range(1, 5):
                if i < len(parts):
                    choice = parts[i]
                    # Премахване на A), B), C), D) префикси
                    for prefix in ['A)', 'B)', 'C)', 'D)', 'a)', 'b)', 'c)', 'd)']:
                        if choice.startswith(prefix):
                            choice = choice[2:].strip()
                            break
                    choices.append(choice)

            # Извличане на верен отговор и обяснение
            correct = ''
            explanation = ''
            for p in parts[5:]:
                p_lower = p.lower()
                if 'верен отговор' in p_lower or 'correct' in p_lower:
                    correct = re.sub(r'.*?(верен отговор|correct)[:\s]*', '', p, flags=re.IGNORECASE).strip()
                elif 'обяснение' in p_lower or 'explanation' in p_lower:
                    explanation = re.sub(r'.*?(обяснение|explanation)[:\s]*', '', p, flags=re.IGNORECASE).strip()

            if question_text and len(choices) >= 2:
                questions.append({
                    'question_text': question_text,
                    'choices': choices[:4],  # Максимум 4 избора
                    'correct_choice': correct or choices[0],  # Fallback към първия избор
                    'explanation': explanation
                })

    # Стратегия 3: Опростен парсинг по блокове
    if not questions:
        blocks = re.split(r'\n\s*\n', ai_output.strip())
        for block in blocks:
            lines = [l.strip() for l in block.split('\n') if l.strip()]
            if len(lines) >= 3:
                question_text = lines[0]
                choices = []
                correct = ''
                explanation = ''

                for line in lines[1:]:
                    # Търсене на избори
                    if re.match(r'^[A-Da-d][).]', line):
                        choice = re.sub(r'^[A-Da-d][).]\s*', '', line)
                        choices.append(choice)
                    # Търсене на верен отговор
                    elif 'верен' in line.lower() or 'correct' in line.lower():
                        correct = re.sub(r'.*?(верен отговор|correct)[:\s]*', '', line, flags=re.IGNORECASE).strip()
                    # Търсене на обяснение
                    elif 'обяснение' in line.lower() or 'explanation' in line.lower():
                        explanation = re.sub(r'.*?(обяснение|explanation)[:\s]*', '', line, flags=re.IGNORECASE).strip()

                if question_text and len(choices) >= 2:
                    questions.append({
                        'question_text': question_text,
                        'choices': choices[:4],
                        'correct_choice': correct or choices[0],
                        'explanation': explanation
                    })

    # Fallback стратегия: Създаване на базови въпроси
    if not questions:
        logger.warning('All parsing strategies failed, creating fallback questions')
        lines = [l.strip() for l in ai_output.split('\n') if l.strip() and len(l.strip()) > 10]
        for i, line in enumerate(lines[:5]):  # Максимум 5 fallback въпроса
            questions.append({
                'question_text': line,
                'choices': [f"Вариант А", f"Вариант Б", f"Вариант В", f"Вариант Г"],
                'correct_choice': "Вариант А",
                'explanation': "Автоматично генериран въпрос - моля прегледайте."
            })

    logger.info(f'Successfully parsed {len(questions)} questions')
    return questions[:20]  # Ограничение до 20 въпроса

def parse_flashcards(ai_output: str):
    """
    Робустен парсер за флашкарти с множество формати и fallback стратегии.
    """
    import json
    import logging

    logger = logging.getLogger(__name__)
    logger.info(f'Parsing AI flashcards output: {ai_output[:200]}...')

    flashcards = []

    # Стратегия 1: JSON парсване
    try:
        if ai_output.strip().startswith('[') or ai_output.strip().startswith('{'):
            json_data = json.loads(ai_output)
            if isinstance(json_data, list):
                for item in json_data:
                    if 'front' in item and 'back' in item:
                        flashcards.append({
                            'front': str(item['front']).strip(),
                            'back': str(item['back']).strip()
                        })
                if flashcards:
                    logger.info(f'Successfully parsed {len(flashcards)} flashcards from JSON')
                    return flashcards[:50]  # Ограничение до 50 флашкарти
    except (json.JSONDecodeError, KeyError, TypeError) as e:
        logger.warning(f'JSON parsing failed: {e}')

    # Стратегия 2: Парсване по разделители
    separators = [' - ', ' | ', ' : ', '\t', ' → ', ' -> ']

    for line in ai_output.strip().split('\n'):
        line = line.strip()
        if not line or len(line) < 5:
            continue

        # Премахване на номерация в началото
        line = re.sub(r'^\d+[.)]\s*', '', line)

        # Опит с различни разделители
        for sep in separators:
            if sep in line:
                parts = line.split(sep, 1)
                if len(parts) == 2:
                    front, back = parts
                    front = front.strip()
                    back = back.strip()

                    if front and back and len(front) > 1 and len(back) > 1:
                        flashcards.append({
                            'front': front,
                            'back': back
                        })
                        break

    # Стратегия 3: Парсване по блокове (въпрос/отговор формат)
    if not flashcards:
        # Търсене на Q: A: формат
        qa_pattern = r'(?:Q|Въпрос|Question)[:\s]*(.+?)(?:A|Отговор|Answer)[:\s]*(.+?)(?=(?:Q|Въпрос|Question)|$)'
        matches = re.findall(qa_pattern, ai_output, re.IGNORECASE | re.DOTALL)

        for front, back in matches:
            front = front.strip()
            back = back.strip()
            if front and back:
                flashcards.append({
                    'front': front,
                    'back': back
                })

    # Стратегия 4: Парсване по двойки редове
    if not flashcards:
        lines = [l.strip() for l in ai_output.split('\n') if l.strip() and len(l.strip()) > 2]
        for i in range(0, len(lines) - 1, 2):
            if i + 1 < len(lines):
                front = lines[i]
                back = lines[i + 1]

                # Премахване на префикси като "Front:", "Back:"
                front = re.sub(r'^(Front|Въпрос|Question)[:\s]*', '', front, flags=re.IGNORECASE)
                back = re.sub(r'^(Back|Отговор|Answer)[:\s]*', '', back, flags=re.IGNORECASE)

                if front and back and len(front) > 1 and len(back) > 1:
                    flashcards.append({
                        'front': front,
                        'back': back
                    })

    # Fallback стратегия
    if not flashcards:
        logger.warning('All parsing strategies failed, creating fallback flashcards')
        lines = [l.strip() for l in ai_output.split('\n') if l.strip() and len(l.strip()) > 5]
        for i, line in enumerate(lines[:10]):  # Максимум 10 fallback флашкарти
            flashcards.append({
                'front': f"Термин {i+1}: {line[:50]}...",
                'back': "Моля прегледайте и редактирайте това определение."
            })

    logger.info(f'Successfully parsed {len(flashcards)} flashcards')
    return flashcards[:50]  # Ограничение до 50 флашкарти

def parse_explanations(ai_output: str):
    """
    Робустен парсер за обяснения/термини с множество формати и fallback стратегии.
    """
    import json
    import logging

    logger = logging.getLogger(__name__)
    logger.info(f'Parsing AI explanations output: {ai_output[:200]}...')

    explanations = []

    # Стратегия 1: JSON парсване
    try:
        if ai_output.strip().startswith('[') or ai_output.strip().startswith('{'):
            json_data = json.loads(ai_output)
            if isinstance(json_data, list):
                for item in json_data:
                    if 'term' in item and 'definition' in item:
                        explanations.append({
                            'term': str(item['term']).strip(),
                            'definition': str(item['definition']).strip()
                        })
                if explanations:
                    logger.info(f'Successfully parsed {len(explanations)} explanations from JSON')
                    return explanations[:30]  # Ограничение до 30 обяснения
    except (json.JSONDecodeError, KeyError, TypeError) as e:
        logger.warning(f'JSON parsing failed: {e}')

    # Стратегия 2: Парсване по разделители
    separators = [': ', ' - ', ' | ', ' → ', ' -> ', ' = ', ' означава ']

    for line in ai_output.strip().split('\n'):
        line = line.strip()
        if not line or len(line) < 5:
            continue

        # Премахване на номерация в началото
        line = re.sub(r'^\d+[.)]\s*', '', line)

        # Опит с различни разделители
        for sep in separators:
            if sep in line:
                parts = line.split(sep, 1)
                if len(parts) == 2:
                    term, definition = parts
                    term = term.strip()
                    definition = definition.strip()

                    if term and definition and len(term) > 1 and len(definition) > 3:
                        explanations.append({
                            'term': term,
                            'definition': definition
                        })
                        break

    # Стратегия 3: Парсване на блокове с заглавия
    if not explanations:
        # Търсене на формат: **Термин** - дефиниция
        bold_pattern = r'\*\*(.+?)\*\*[:\s-]*(.+?)(?=\*\*|$)'
        matches = re.findall(bold_pattern, ai_output, re.DOTALL)

        for term, definition in matches:
            term = term.strip()
            definition = definition.strip()
            if term and definition:
                explanations.append({
                    'term': term,
                    'definition': definition
                })

    # Стратегия 4: Парсване по двойки редове
    if not explanations:
        lines = [l.strip() for l in ai_output.split('\n') if l.strip() and len(l.strip()) > 2]

        # Търсене на термини в отделни редове, следвани от дефиниции
        i = 0
        while i < len(lines) - 1:
            current_line = lines[i]
            next_line = lines[i + 1]

            # Ако текущият ред е кратък (вероятно термин) и следващият е по-дълъг (дефиниция)
            if len(current_line) < 50 and len(next_line) > len(current_line):
                term = current_line
                definition = next_line

                # Премахване на префикси
                term = re.sub(r'^(Термин|Term)[:\s]*', '', term, flags=re.IGNORECASE)
                definition = re.sub(r'^(Дефиниция|Definition)[:\s]*', '', definition, flags=re.IGNORECASE)

                if term and definition and len(definition) > 5:
                    explanations.append({
                        'term': term,
                        'definition': definition
                    })
                    i += 2  # Прескачаме следващия ред
                    continue
            i += 1

    # Стратегия 5: Извличане на термини от текст
    if not explanations:
        # Търсене на изречения, които дефинират термини
        definition_patterns = [
            r'(.+?)\s+(?:е|са|означава|представлява|се нарича)\s+(.+?)(?:\.|$)',
            r'(.+?)\s+(?:is|are|means|represents)\s+(.+?)(?:\.|$)'
        ]

        for pattern in definition_patterns:
            matches = re.findall(pattern, ai_output, re.IGNORECASE)
            for term, definition in matches:
                term = term.strip()
                definition = definition.strip()

                if (term and definition and
                    len(term) < 100 and len(definition) > 5 and
                    not term.lower().startswith(('това', 'this', 'that'))):
                    explanations.append({
                        'term': term,
                        'definition': definition
                    })

    # Fallback стратегия
    if not explanations:
        logger.warning('All parsing strategies failed, creating fallback explanations')
        lines = [l.strip() for l in ai_output.split('\n') if l.strip() and len(l.strip()) > 10]
        for i, line in enumerate(lines[:10]):  # Максимум 10 fallback обяснения
            # Опит да извлечем първата дума като термин
            words = line.split()
            if len(words) > 3:
                term = words[0]
                definition = ' '.join(words[1:])
                explanations.append({
                    'term': term,
                    'definition': definition
                })

    logger.info(f'Successfully parsed {len(explanations)} explanations')
    return explanations[:30]  # Ограничение до 30 обяснения

# @shared_task - Временно коментирано, AI генерирането се прави директно във views
def process_uploaded_document_old(document_id):
    """
    DEPRECATED: Стара Celery задача за обработка на качен документ.
    Сега AI генерирането се прави директно във views с новата структура.
    """
    pass
    # try:
    #     doc = Document.objects.get(id=document_id)
    #     file_path = doc.file.path
    #     extracted_text = extract_text(file_path)
    #     # AI заявки с кеширане
    #     client = OpenAIClient()
    #     summary = client.generate_completion(generate_summary_prompt(extracted_text), content_type='summary')
    #     questions_ai = client.generate_completion(generate_questions_prompt(extracted_text), content_type='questions')
    #     flashcards_ai = client.generate_completion(generate_flashcards_prompt(extracted_text), content_type='flashcards')
    #     explanations_ai = client.generate_completion(generate_explanations_prompt(extracted_text), content_type='explanations')
    #     # Парсване и запис
    #     for q in parse_questions(questions_ai):
    #         Question.objects.create(document=doc, **q)
    #     for f in parse_flashcards(flashcards_ai):
    #         Flashcard.objects.create(document=doc, **f)
    #     for e in parse_explanations(explanations_ai):
    #         Explanation.objects.create(document=doc, **e)
    #     doc.text_content = f"[Резюме]\n{summary}"
    #     doc.status = 'ready'
    #     doc.save()
    # except Exception as e:
    #     Document.objects.filter(id=document_id).update(status='failed')
    #     print(f"Document processing failed: {e}")
        