"""
Подобрена структура на моделите за реална употреба.
"""
import uuid
from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinLengthValidator, MaxLengthValidator
from django.utils import timezone
import json

class UserProfile(models.Model):
    """Разширен потребителски профил."""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Настройки
    preferred_language = models.CharField(max_length=10, default='bg', choices=[
        ('bg', 'Български'),
        ('en', 'English'),
    ])
    ai_difficulty_level = models.CharField(max_length=20, default='medium', choices=[
        ('easy', 'Лесно'),
        ('medium', 'Средно'),
        ('hard', 'Трудно'),
    ])

    # Статистики
    total_documents_uploaded = models.PositiveIntegerField(default=0)
    total_tests_taken = models.PositiveIntegerField(default=0)
    total_study_time_minutes = models.PositiveIntegerField(default=0)

    def __str__(self):
        return f"Profile: {self.user.username}"

class Course(models.Model):
    """Курс/предмет за организиране на документи."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='courses')

    name = models.CharField(max_length=200, validators=[MinLengthValidator(2)])
    description = models.TextField(blank=True)
    color = models.CharField(max_length=7, default='#3498db')  # Hex color

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-updated_at']
        unique_together = ['owner', 'name']

    def __str__(self):
        return f"{self.name} ({self.owner.username})"

class Document(models.Model):
    """Подобрен модел за документи."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='documents')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='documents', null=True, blank=True)

    # Файл информация
    title = models.CharField(max_length=200, validators=[MinLengthValidator(2)])
    file = models.FileField(upload_to='documents/%Y/%m/')
    file_size = models.PositiveIntegerField()  # в байтове
    file_type = models.CharField(max_length=10, choices=[
        ('pdf', 'PDF'),
        ('docx', 'DOCX'),
        ('txt', 'TXT'),
    ])

    # Обработка
    status = models.CharField(max_length=20, default='uploaded', choices=[
        ('uploaded', 'Качен'),
        ('processing', 'Обработва се'),
        ('ready', 'Готов'),
        ('failed', 'Неуспешен'),
    ])
    extracted_text = models.TextField(blank=True)
    ai_summary = models.TextField(blank=True)

    # Метаданни
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_accessed = models.DateTimeField(null=True, blank=True)

    # Статистики
    view_count = models.PositiveIntegerField(default=0)
    study_sessions_count = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['-updated_at']

    def __str__(self):
        return f"{self.title} ({self.owner.username})"

    def mark_accessed(self):
        """Отбелязва документа като достъпен."""
        self.last_accessed = timezone.now()
        self.view_count += 1
        self.save(update_fields=['last_accessed', 'view_count'])

class StudySet(models.Model):
    """Колекция от учебни материали (флашкарти, въпроси, etc.)"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='study_sets')

    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)

    # Типове съдържание
    content_type = models.CharField(max_length=20, choices=[
        ('flashcards', 'Флашкарти'),
        ('quiz', 'Тест'),
        ('explanations', 'Обяснения'),
        ('mixed', 'Смесено'),
    ])

    # AI настройки
    ai_prompt_used = models.TextField(blank=True)
    ai_model_version = models.CharField(max_length=50, default='gpt-3.5-turbo')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Статистики
    times_studied = models.PositiveIntegerField(default=0)
    average_score = models.FloatField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.content_type})"

class Flashcard(models.Model):
    """Подобрени флашкарти."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    study_set = models.ForeignKey(StudySet, on_delete=models.CASCADE, related_name='flashcards')

    front = models.TextField(validators=[MinLengthValidator(2)])
    back = models.TextField(validators=[MinLengthValidator(2)])

    # Допълнителни полета
    difficulty = models.CharField(max_length=10, default='medium', choices=[
        ('easy', 'Лесно'),
        ('medium', 'Средно'),
        ('hard', 'Трудно'),
    ])
    tags = models.JSONField(default=list, blank=True)  # ['математика', 'алгебра']

    # Spaced repetition данни
    ease_factor = models.FloatField(default=2.5)
    interval_days = models.PositiveIntegerField(default=1)
    next_review_date = models.DateTimeField(default=timezone.now)
    times_reviewed = models.PositiveIntegerField(default=0)
    times_correct = models.PositiveIntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['next_review_date']

    def __str__(self):
        return f"{self.front[:50]}..."

    @property
    def success_rate(self):
        """Процент успешни отговори."""
        if self.times_reviewed == 0:
            return 0
        return (self.times_correct / self.times_reviewed) * 100

class Quiz(models.Model):
    """Тест/викторина."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    study_set = models.ForeignKey(StudySet, on_delete=models.CASCADE, related_name='quizzes')

    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)

    # Настройки
    time_limit_minutes = models.PositiveIntegerField(null=True, blank=True)
    shuffle_questions = models.BooleanField(default=True)
    show_correct_answers = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Статистики
    times_taken = models.PositiveIntegerField(default=0)
    average_score = models.FloatField(null=True, blank=True)

    def __str__(self):
        return self.title

class Question(models.Model):
    """Въпрос в тест."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    quiz = models.ForeignKey(Quiz, on_delete=models.CASCADE, related_name='questions')

    question_text = models.TextField(validators=[MinLengthValidator(5)])
    question_type = models.CharField(max_length=20, default='multiple_choice', choices=[
        ('multiple_choice', 'Избираем отговор'),
        ('true_false', 'Вярно/Невярно'),
        ('short_answer', 'Кратък отговор'),
        ('essay', 'Есе'),
    ])

    # За multiple choice
    choices = models.JSONField(default=list)  # ['A) Отговор 1', 'B) Отговор 2', ...]
    correct_choice = models.CharField(max_length=200, blank=True)

    # За други типове
    correct_answer = models.TextField(blank=True)

    explanation = models.TextField(blank=True)
    points = models.PositiveIntegerField(default=1)
    order = models.PositiveIntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order', 'created_at']

    def __str__(self):
        return f"{self.question_text[:50]}..."

class StudySession(models.Model):
    """Сесия на учене."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='study_sessions')
    study_set = models.ForeignKey(StudySet, on_delete=models.CASCADE, related_name='sessions')

    session_type = models.CharField(max_length=20, choices=[
        ('flashcards', 'Флашкарти'),
        ('quiz', 'Тест'),
        ('review', 'Преглед'),
    ])

    started_at = models.DateTimeField(auto_now_add=True)
    ended_at = models.DateTimeField(null=True, blank=True)
    duration_minutes = models.PositiveIntegerField(null=True, blank=True)

    # Резултати
    total_items = models.PositiveIntegerField(default=0)
    correct_answers = models.PositiveIntegerField(default=0)
    score_percentage = models.FloatField(null=True, blank=True)

    # Метаданни
    completed = models.BooleanField(default=False)
    notes = models.TextField(blank=True)

    def __str__(self):
        return f"{self.user.username} - {self.study_set.name} ({self.started_at.date()})"

    def calculate_score(self):
        """Изчислява резултата."""
        if self.total_items > 0:
            self.score_percentage = (self.correct_answers / self.total_items) * 100
        else:
            self.score_percentage = 0
        self.save(update_fields=['score_percentage'])

class FlashcardReview(models.Model):
    """Преглед на флашкарта (за spaced repetition)."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    session = models.ForeignKey(StudySession, on_delete=models.CASCADE, related_name='flashcard_reviews')
    flashcard = models.ForeignKey(Flashcard, on_delete=models.CASCADE, related_name='reviews')

    response_quality = models.IntegerField(choices=[
        (0, 'Не знам'),
        (1, 'Трудно'),
        (2, 'Добре'),
        (3, 'Лесно'),
        (4, 'Перфектно'),
    ])

    response_time_seconds = models.PositiveIntegerField()
    reviewed_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.flashcard.front[:30]}... - {self.response_quality}"

class QuizAttempt(models.Model):
    """Опит за решаване на тест."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    session = models.ForeignKey(StudySession, on_delete=models.CASCADE, related_name='quiz_attempts')
    quiz = models.ForeignKey(Quiz, on_delete=models.CASCADE, related_name='attempts')

    answers = models.JSONField(default=dict)  # {question_id: answer}
    score = models.FloatField(null=True, blank=True)
    max_score = models.FloatField(null=True, blank=True)

    started_at = models.DateTimeField(auto_now_add=True)
    submitted_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.quiz.title} - {self.score}/{self.max_score}"

# Премахнати backward compatibility модели - ще използваме нова база данни
