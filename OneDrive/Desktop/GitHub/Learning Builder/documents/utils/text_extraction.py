import os

# Импортирай нужните библиотеки
try:
    import PyPDF2
except ImportError:
    PyPDF2 = None
try:
    from docx import Document
    docx_available = True
except ImportError:
    Document = None
    docx_available = False

def extract_text(file_path: str) -> str:
    """
    Извлича текста от файл (PDF, DOCX, TXT) според разширението.
    :param file_path: път до файла
    :return: извлечен текст като string
    """
    ext = os.path.splitext(file_path)[1].lower()
    if ext == '.txt':
        return _extract_txt(file_path)
    elif ext == '.pdf':
        return _extract_pdf(file_path)
    elif ext == '.docx':
        return _extract_docx(file_path)
    else:
        raise ValueError('Неподдържан файлов формат')

def _extract_txt(file_path: str) -> str:
    """Извлича текст от .txt файл."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

def _extract_pdf(file_path: str) -> str:
    """Извлича текст от PDF файл с PyPDF2."""
    if not PyPDF2:
        raise ImportError('PyPDF2 не е инсталиран')
    text = []
    with open(file_path, 'rb') as f:
        reader = PyPDF2.PdfReader(f)
        for page in reader.pages:
            text.append(page.extract_text() or "")
    return '\n'.join(text)

def _extract_docx(file_path: str) -> str:
    """Извлича текст от DOCX файл с python-docx."""
    if not docx_available:
        raise ImportError('python-docx не е инсталиран')
    doc = Document(file_path)
    return '\n'.join([p.text for p in doc.paragraphs])
