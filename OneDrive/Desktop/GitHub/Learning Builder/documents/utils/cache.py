"""
Caching utilities for AI results to reduce OpenAI API calls and improve performance.
"""
import hashlib
import json
import logging
from django.core.cache import cache
from django.conf import settings

logger = logging.getLogger(__name__)

class AIResultCache:
    """
    Cache manager for AI-generated content to reduce API calls and costs.
    """
    
    # Cache timeouts (in seconds)
    CACHE_TIMEOUT_QUESTIONS = 60 * 60 * 24 * 7  # 7 days
    CACHE_TIMEOUT_FLASHCARDS = 60 * 60 * 24 * 7  # 7 days
    CACHE_TIMEOUT_EXPLANATIONS = 60 * 60 * 24 * 7  # 7 days
    CACHE_TIMEOUT_SUMMARY = 60 * 60 * 24 * 30  # 30 days
    
    @staticmethod
    def _generate_cache_key(content_type: str, text_hash: str) -> str:
        """
        Generate a unique cache key based on content type and text hash.
        """
        return f"ai_result:{content_type}:{text_hash}"
    
    @staticmethod
    def _hash_text(text: str) -> str:
        """
        Generate a hash of the input text for cache key generation.
        """
        # Normalize text: remove extra whitespace, convert to lowercase
        normalized_text = ' '.join(text.lower().split())
        return hashlib.sha256(normalized_text.encode('utf-8')).hexdigest()[:16]
    
    @classmethod
    def get_cached_result(cls, content_type: str, text: str):
        """
        Retrieve cached AI result if available.
        
        Args:
            content_type: Type of content ('questions', 'flashcards', 'explanations', 'summary')
            text: Input text that was processed
            
        Returns:
            Cached result or None if not found
        """
        if not getattr(settings, 'RATELIMIT_ENABLE', True):
            return None
            
        text_hash = cls._hash_text(text)
        cache_key = cls._generate_cache_key(content_type, text_hash)
        
        try:
            cached_result = cache.get(cache_key)
            if cached_result:
                logger.info(f"Cache hit for {content_type} with key {cache_key}")
                return cached_result
            else:
                logger.info(f"Cache miss for {content_type} with key {cache_key}")
                return None
        except Exception as e:
            logger.error(f"Cache retrieval error: {e}")
            return None
    
    @classmethod
    def cache_result(cls, content_type: str, text: str, result: str):
        """
        Cache AI result for future use.
        
        Args:
            content_type: Type of content ('questions', 'flashcards', 'explanations', 'summary')
            text: Input text that was processed
            result: AI-generated result to cache
        """
        if not getattr(settings, 'RATELIMIT_ENABLE', True):
            return
            
        text_hash = cls._hash_text(text)
        cache_key = cls._generate_cache_key(content_type, text_hash)
        
        # Determine cache timeout based on content type
        timeout_map = {
            'questions': cls.CACHE_TIMEOUT_QUESTIONS,
            'flashcards': cls.CACHE_TIMEOUT_FLASHCARDS,
            'explanations': cls.CACHE_TIMEOUT_EXPLANATIONS,
            'summary': cls.CACHE_TIMEOUT_SUMMARY,
        }
        timeout = timeout_map.get(content_type, cls.CACHE_TIMEOUT_QUESTIONS)
        
        try:
            cache.set(cache_key, result, timeout)
            logger.info(f"Cached {content_type} result with key {cache_key} for {timeout} seconds")
        except Exception as e:
            logger.error(f"Cache storage error: {e}")
    
    @classmethod
    def invalidate_cache(cls, content_type: str, text: str):
        """
        Invalidate cached result for specific content.
        
        Args:
            content_type: Type of content to invalidate
            text: Input text to invalidate cache for
        """
        text_hash = cls._hash_text(text)
        cache_key = cls._generate_cache_key(content_type, text_hash)
        
        try:
            cache.delete(cache_key)
            logger.info(f"Invalidated cache for {content_type} with key {cache_key}")
        except Exception as e:
            logger.error(f"Cache invalidation error: {e}")
    
    @classmethod
    def clear_all_cache(cls):
        """
        Clear all AI result cache entries.
        """
        try:
            # This is a simple implementation - in production you might want
            # to use cache versioning or more sophisticated cache management
            cache.clear()
            logger.info("Cleared all AI result cache")
        except Exception as e:
            logger.error(f"Cache clear error: {e}")

def get_cache_stats():
    """
    Get cache statistics (if supported by the cache backend).
    """
    try:
        # This works with Redis backend
        from django_redis import get_redis_connection
        redis_conn = get_redis_connection("default")
        info = redis_conn.info()
        return {
            'used_memory': info.get('used_memory_human', 'N/A'),
            'connected_clients': info.get('connected_clients', 'N/A'),
            'total_commands_processed': info.get('total_commands_processed', 'N/A'),
            'keyspace_hits': info.get('keyspace_hits', 'N/A'),
            'keyspace_misses': info.get('keyspace_misses', 'N/A'),
        }
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        return {'error': str(e)}
