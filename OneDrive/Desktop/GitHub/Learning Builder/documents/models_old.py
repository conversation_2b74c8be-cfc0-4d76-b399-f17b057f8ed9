import uuid
from django.db import models

# Create your models here.

class UploadedDocument(models.Model):
    """
    Модел за качен документ, който се обработва от AI.
    """
    STATUS_CHOICES = [
        ("processing", "Processing"),
        ("ready", "Ready"),
        ("failed", "Failed"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255, blank=True)
    file = models.FileField(upload_to="documents/")
    text_content = models.TextField(blank=True)
    status = models.CharField(max_length=16, choices=STATUS_CHOICES, default="processing")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title or str(self.id)

class Question(models.Model):
    """
    Въпрос с избираем отговор, генериран от AI.
    """
    document = models.ForeignKey(UploadedDocument, on_delete=models.CASCADE, related_name='questions')
    question_text = models.TextField()
    choices = models.JSONField()
    correct_choice = models.CharField(max_length=255)
    explanation = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.question_text[:50]

class Explanation(models.Model):
    """
    Обяснение на термин, генерирано от AI.
    """
    document = models.ForeignKey(UploadedDocument, on_delete=models.CASCADE, related_name='explanations')
    term = models.CharField(max_length=255)
    definition = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.term

class Flashcard(models.Model):
    """
    Флашкарта (въпрос/отговор), генерирана от AI.
    """
    document = models.ForeignKey(UploadedDocument, on_delete=models.CASCADE, related_name='flashcards')
    front = models.TextField()
    back = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.front[:50]
