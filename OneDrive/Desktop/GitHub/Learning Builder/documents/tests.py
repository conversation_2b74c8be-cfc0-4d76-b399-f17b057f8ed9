import importlib
import json
import tempfile
import os
from unittest.mock import patch, MagicMock

reportlab_installed = importlib.util.find_spec('reportlab') is not None
genanki_installed = importlib.util.find_spec('genanki') is not None

from django.test import TestCase, override_settings
from django.urls import reverse
from django.core.cache import cache
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from .models import UploadedDocument, Question, Flashcard, Explanation
from .tasks import parse_questions, parse_flashcards, parse_explanations
from .serializers import validate_file_extension, validate_file_size, validate_file_content
from documents.utils.cache import AIResultCache
from documents.ai.client import OpenAIClient
from django.core.files.uploadedfile import SimpleUploadedFile
import time
from unittest import mock, skipIf

# Create your tests here.

class DocumentAPITests(APITestCase):
    """
    Базови интеграционни тестове за upload и AI endpoints.
    """
    def setUp(self):
        self.client = APIClient()
        self.upload_url = reverse('document-upload')

    def test_upload_and_status(self):
        """Тества качване и проверка на статус на документ."""
        file = SimpleUploadedFile('test.txt', b'AI test content', content_type='text/plain')
        response = self.client.post(self.upload_url, {'file': file}, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        doc_id = response.data['id']
        # Изчакай Celery задачата (или симулирай)
        time.sleep(2)
        status_url = reverse('document-status', kwargs={'pk': doc_id})
        status_resp = self.client.get(status_url)
        self.assertEqual(status_resp.status_code, 200)
        self.assertIn('status', status_resp.data)

    def test_questions_flashcards_explanations(self):
        """Тества достъп до въпроси, флашкарти и обяснения по документ."""
        doc = UploadedDocument.objects.create(file=SimpleUploadedFile('a.txt', b'test'), status='ready')
        Question.objects.create(document=doc, question_text='Q?', choices=['A','B'], correct_choice='A')
        Flashcard.objects.create(document=doc, front='Front', back='Back')
        Explanation.objects.create(document=doc, term='AI', definition='Изкуствен интелект')
        q_url = reverse('document-questions', kwargs={'document_id': doc.id})
        f_url = reverse('document-flashcards', kwargs={'document_id': doc.id})
        e_url = reverse('document-explanations', kwargs={'document_id': doc.id})
        self.assertEqual(self.client.get(q_url).status_code, 200)
        self.assertEqual(self.client.get(f_url).status_code, 200)
        self.assertEqual(self.client.get(e_url).status_code, 200)

    def test_upload_unsupported_filetype(self):
        """Тества качване на неподдържан файлов формат."""
        file = SimpleUploadedFile('test.exe', b'fake', content_type='application/octet-stream')
        response = self.client.post(self.upload_url, {'file': file}, format='multipart')
        self.assertEqual(response.status_code, 400)
        self.assertIn('file', response.data)

    def test_upload_empty_file(self):
        """Тества качване на празен файл."""
        file = SimpleUploadedFile('test.txt', b'', content_type='text/plain')
        response = self.client.post(self.upload_url, {'file': file}, format='multipart')
        self.assertEqual(response.status_code, 400)

    def test_upload_no_file(self):
        """Тества качване без файл."""
        response = self.client.post(self.upload_url, {}, format='multipart')
        self.assertEqual(response.status_code, 400)
        self.assertIn('file', response.data)

    def test_status_invalid_id(self):
        """Тества статус endpoint с невалиден document_id."""
        status_url = reverse('document-status', kwargs={'pk': '00000000-0000-0000-0000-000000000000'})
        response = self.client.get(status_url)
        self.assertEqual(response.status_code, 404)

    def test_list_invalid_id(self):
        """Тества въпроси/флашкарти/обяснения endpoint с невалиден document_id."""
        for name in ['document-questions', 'document-flashcards', 'document-explanations']:
            url = reverse(name, kwargs={'document_id': '00000000-0000-0000-0000-000000000000'})
            response = self.client.get(url)
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.data, [])

    @mock.patch('documents.ai.client.OpenAIClient.generate_completion', side_effect=Exception('AI error'))
    def test_ai_processing_error(self, mock_ai):
        """Тества обработка при грешка от AI (симулирана)."""
        file = SimpleUploadedFile('test.txt', b'AI test content', content_type='text/plain')
        response = self.client.post(self.upload_url, {'file': file}, format='multipart')
        self.assertEqual(response.status_code, 201)
        doc_id = response.data['id']
        time.sleep(2)
        status_url = reverse('document-status', kwargs={'pk': doc_id})
        status_resp = self.client.get(status_url)
        self.assertEqual(status_resp.data['status'], 'failed')

    @skipIf(not reportlab_installed, "reportlab не е инсталиран")
    def test_pdf_export(self):
        """Тества PDF експорт на въпроси за документ."""
        doc = UploadedDocument.objects.create(file=SimpleUploadedFile('a.txt', b'test'), status='ready')
        Question.objects.create(document=doc, question_text='Q?', choices=['A','B'], correct_choice='A')
        url = reverse('document-export-pdf', kwargs={'document_id': doc.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['content-type'], 'application/pdf')
        file_bytes = b''.join(response.streaming_content)
        self.assertIn(b'%PDF', file_bytes[:10])

    @skipIf(not reportlab_installed, "reportlab не е инсталиран")
    def test_pdf_export_no_questions(self):
        """Тества PDF експорт при липса на въпроси."""
        doc = UploadedDocument.objects.create(file=SimpleUploadedFile('a.txt', b'test'), status='ready')
        url = reverse('document-export-pdf', kwargs={'document_id': doc.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    @skipIf(not reportlab_installed, "reportlab не е инсталиран")
    def test_pdf_export_invalid_doc(self):
        """Тества PDF експорт при невалиден document_id."""
        url = reverse('document-export-pdf', kwargs={'document_id': '00000000-0000-0000-0000-000000000000'})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    @skipIf(not genanki_installed, "genanki не е инсталиран")
    def test_anki_export(self):
        """Тества Anki експорт на флашкарти за документ."""
        doc = UploadedDocument.objects.create(file=SimpleUploadedFile('a.txt', b'test'), status='ready')
        Flashcard.objects.create(document=doc, front='Front', back='Back')
        url = reverse('document-export-anki', kwargs={'document_id': doc.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['content-type'], 'application/octet-stream')
        file_bytes = b''.join(response.streaming_content)
        self.assertGreater(len(file_bytes), 1024)
        self.assertIn(b'SQLite format', file_bytes[:100])

class AIParsingTests(TestCase):
    """
    Тестове за AI парсърите с различни входни формати.
    """

    def test_parse_questions_json_format(self):
        """Тества парсване на въпроси в JSON формат."""
        json_input = '''[
            {
                "question_text": "Какво е AI?",
                "choices": ["Изкуствен интелект", "Автоматична информация", "Алгоритмична интеграция", "Нищо от изброените"],
                "correct_choice": "Изкуствен интелект",
                "explanation": "AI означава Artificial Intelligence"
            }
        ]'''

        questions = parse_questions(json_input)
        self.assertEqual(len(questions), 1)
        self.assertEqual(questions[0]['question_text'], "Какво е AI?")
        self.assertEqual(questions[0]['correct_choice'], "Изкуствен интелект")

    def test_parse_questions_pipe_format(self):
        """Тества парсване на въпроси с pipe разделители."""
        pipe_input = "Какво е Python? | A) Змия | B) Програмен език | C) Математическа формула | D) Нищо от изброените | Верен отговор: B) Програмен език | Обяснение: Python е програмен език"

        questions = parse_questions(pipe_input)
        self.assertEqual(len(questions), 1)
        self.assertEqual(questions[0]['question_text'], "Какво е Python?")
        self.assertIn("Програмен език", questions[0]['choices'])

    def test_parse_questions_fallback(self):
        """Тества fallback механизма при неразпознат формат."""
        random_input = "Това е случаен текст без структура"

        questions = parse_questions(random_input)
        self.assertGreater(len(questions), 0)  # Трябва да създаде поне един fallback въпрос
        self.assertIn("Вариант", questions[0]['choices'][0])

    def test_parse_flashcards_json_format(self):
        """Тества парсване на флашкарти в JSON формат."""
        json_input = '''[
            {"front": "Какво е Django?", "back": "Web framework за Python"},
            {"front": "Какво е REST?", "back": "Representational State Transfer"}
        ]'''

        flashcards = parse_flashcards(json_input)
        self.assertEqual(len(flashcards), 2)
        self.assertEqual(flashcards[0]['front'], "Какво е Django?")
        self.assertEqual(flashcards[0]['back'], "Web framework за Python")

    def test_parse_flashcards_dash_format(self):
        """Тества парсване на флашкарти с тире разделители."""
        dash_input = """API - Application Programming Interface
HTTP - HyperText Transfer Protocol"""

        flashcards = parse_flashcards(dash_input)
        self.assertEqual(len(flashcards), 2)
        self.assertEqual(flashcards[0]['front'], "API")
        self.assertEqual(flashcards[0]['back'], "Application Programming Interface")

    def test_parse_explanations_colon_format(self):
        """Тества парсване на обяснения с двоеточие."""
        colon_input = """API: Application Programming Interface за комуникация между приложения
Framework: Структура за разработка на софтуер"""

        explanations = parse_explanations(colon_input)
        self.assertEqual(len(explanations), 2)
        self.assertEqual(explanations[0]['term'], "API")
        self.assertIn("Application Programming Interface", explanations[0]['definition'])

class FileValidationTests(TestCase):
    """
    Тестове за валидация на файлове.
    """

    def test_valid_file_extensions(self):
        """Тества валидни файлови разширения."""
        valid_files = [
            SimpleUploadedFile('test.pdf', b'content', content_type='application/pdf'),
            SimpleUploadedFile('test.docx', b'content', content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'),
            SimpleUploadedFile('test.txt', b'content', content_type='text/plain'),
        ]

        for file in valid_files:
            try:
                validate_file_extension(file)
            except Exception as e:
                self.fail(f"Valid file {file.name} failed validation: {e}")

    def test_invalid_file_extensions(self):
        """Тества невалидни файлови разширения."""
        invalid_files = [
            SimpleUploadedFile('test.exe', b'content'),
            SimpleUploadedFile('test.jpg', b'content'),
            SimpleUploadedFile('test.mp3', b'content'),
        ]

        for file in invalid_files:
            with self.assertRaises(Exception):
                validate_file_extension(file)

    def test_file_size_validation(self):
        """Тества валидация на размера на файла."""
        # Малък файл - трябва да мине
        small_file = SimpleUploadedFile('small.txt', b'x' * 1000)
        try:
            validate_file_size(small_file)
        except Exception as e:
            self.fail(f"Small file failed validation: {e}")

        # Голям файл - трябва да не мине
        large_content = b'x' * (51 * 1024 * 1024)  # 51MB
        large_file = SimpleUploadedFile('large.txt', large_content)
        with self.assertRaises(Exception):
            validate_file_size(large_file)

    def test_file_content_validation(self):
        """Тества валидация на съдържанието на файла."""
        # Твърде малък файл
        tiny_file = SimpleUploadedFile('tiny.txt', b'x')
        with self.assertRaises(Exception):
            validate_file_content(tiny_file)

        # Опасен файл
        dangerous_file = SimpleUploadedFile('virus.exe.txt', b'content')
        with self.assertRaises(Exception):
            validate_file_content(dangerous_file)

        # Валиден файл
        valid_file = SimpleUploadedFile('valid.txt', b'x' * 200)
        try:
            validate_file_content(valid_file)
        except Exception as e:
            self.fail(f"Valid file failed validation: {e}")

class CacheTests(TestCase):
    """
    Тестове за кеширане на AI резултати.
    """

    def setUp(self):
        cache.clear()

    def tearDown(self):
        cache.clear()

    def test_cache_and_retrieve(self):
        """Тества кеширане и извличане на резултати."""
        text = "Тестов текст за кеширане"
        result = "Тестов AI резултат"
        content_type = "questions"

        # Кеширане
        AIResultCache.cache_result(content_type, text, result)

        # Извличане
        cached_result = AIResultCache.get_cached_result(content_type, text)
        self.assertEqual(cached_result, result)

    def test_cache_miss(self):
        """Тества липса на кеширан резултат."""
        result = AIResultCache.get_cached_result("questions", "несъществуващ текст")
        self.assertIsNone(result)

    def test_cache_invalidation(self):
        """Тества инвалидиране на кеш."""
        text = "Тестов текст"
        result = "Тестов резултат"
        content_type = "flashcards"

        # Кеширане
        AIResultCache.cache_result(content_type, text, result)

        # Проверка че е кеширан
        cached_result = AIResultCache.get_cached_result(content_type, text)
        self.assertEqual(cached_result, result)

        # Инвалидиране
        AIResultCache.invalidate_cache(content_type, text)

        # Проверка че е премахнат
        cached_result = AIResultCache.get_cached_result(content_type, text)
        self.assertIsNone(cached_result)

class RateLimitingTests(APITestCase):
    """
    Тестове за rate limiting на API endpoints.
    """

    def setUp(self):
        self.client = APIClient()
        cache.clear()

    def tearDown(self):
        cache.clear()

    @override_settings(RATELIMIT_ENABLE=True)
    def test_upload_rate_limiting(self):
        """Тества rate limiting за upload endpoint."""
        upload_url = reverse('document-upload')

        # Първите няколко заявки трябва да минат
        for i in range(3):
            file = SimpleUploadedFile(f'test{i}.txt', b'test content', content_type='text/plain')
            response = self.client.post(upload_url, {'file': file}, format='multipart')
            # Може да е 201 (успех) или 429 (rate limit)
            self.assertIn(response.status_code, [201, 429])

    @override_settings(RATELIMIT_ENABLE=True)
    def test_ai_generation_rate_limiting(self):
        """Тества rate limiting за AI generation endpoints."""
        # Създаване на тестов документ
        doc = UploadedDocument.objects.create(
            file=SimpleUploadedFile('test.txt', b'test content'),
            status='ready'
        )

        questions_url = reverse('generate-questions', kwargs={'document_id': doc.id})

        # Първите няколко заявки трябва да минат
        for i in range(2):
            with patch('documents.ai.client.OpenAIClient.generate_completion') as mock_ai:
                mock_ai.return_value = "Тестов въпрос | A) Отговор 1 | B) Отговор 2 | C) Отговор 3 | D) Отговор 4 | Верен отговор: A | Обяснение: Тест"
                response = self.client.post(questions_url)
                # Може да е 200 (успех) или 429 (rate limit)
                self.assertIn(response.status_code, [200, 429])

class IntegrationTests(APITestCase):
    """
    Интеграционни тестове за цялостния workflow.
    """

    def setUp(self):
        self.client = APIClient()
        cache.clear()

    def tearDown(self):
        cache.clear()

    @patch('documents.ai.client.OpenAIClient.generate_completion')
    def test_full_document_processing_workflow(self, mock_ai):
        """Тества пълния workflow от качване до генериране на съдържание."""
        # Mock AI отговори
        mock_ai.side_effect = [
            "Тестово резюме на документа",  # summary
            "Тестов въпрос | A) Отговор 1 | B) Отговор 2 | C) Отговор 3 | D) Отговор 4 | Верен отговор: A | Обяснение: Тест",  # questions
            "Термин 1 - Дефиниция 1\nТермин 2 - Дефиниция 2",  # flashcards
            "Концепт 1: Обяснение 1\nКонцепт 2: Обяснение 2"  # explanations
        ]

        # 1. Качване на документ
        upload_url = reverse('document-upload')
        file = SimpleUploadedFile('test.txt', b'Тестово съдържание за обработка', content_type='text/plain')
        response = self.client.post(upload_url, {'file': file}, format='multipart')
        self.assertEqual(response.status_code, 201)

        doc_id = response.data['id']

        # 2. Генериране на въпроси
        questions_url = reverse('generate-questions', kwargs={'document_id': doc_id})
        response = self.client.post(questions_url)
        self.assertEqual(response.status_code, 200)

        # 3. Проверка че въпросите са създадени
        questions_list_url = reverse('document-questions', kwargs={'document_id': doc_id})
        response = self.client.get(questions_list_url)
        self.assertEqual(response.status_code, 200)
        self.assertGreater(len(response.data), 0)

        # 4. Генериране на флашкарти
        flashcards_url = reverse('generate-flashcards', kwargs={'document_id': doc_id})
        response = self.client.post(flashcards_url)
        self.assertEqual(response.status_code, 200)

        # 5. Проверка че флашкартите са създадени
        flashcards_list_url = reverse('document-flashcards', kwargs={'document_id': doc_id})
        response = self.client.get(flashcards_list_url)
        self.assertEqual(response.status_code, 200)
        self.assertGreater(len(response.data), 0)

        # 6. Генериране на обяснения
        explanations_url = reverse('generate-explanations', kwargs={'document_id': doc_id})
        response = self.client.post(explanations_url)
        self.assertEqual(response.status_code, 200)

        # 7. Проверка че обясненията са създадени
        explanations_list_url = reverse('document-explanations', kwargs={'document_id': doc_id})
        response = self.client.get(explanations_list_url)
        self.assertEqual(response.status_code, 200)
        self.assertGreater(len(response.data), 0)

    def test_error_handling_invalid_document_id(self):
        """Тества обработката на грешки при невалиден document ID."""
        invalid_id = '00000000-0000-0000-0000-000000000000'

        # Опит за генериране на въпроси с невалиден ID
        questions_url = reverse('generate-questions', kwargs={'document_id': invalid_id})
        response = self.client.post(questions_url)
        self.assertEqual(response.status_code, 404)
        self.assertIn('error', response.data)

    def test_file_validation_integration(self):
        """Тества интеграцията на file validation."""
        upload_url = reverse('document-upload')

        # Тест с невалиден файл
        invalid_file = SimpleUploadedFile('test.exe', b'invalid content')
        response = self.client.post(upload_url, {'file': invalid_file}, format='multipart')
        self.assertEqual(response.status_code, 400)

        # Тест с твърде голям файл
        large_content = b'x' * (51 * 1024 * 1024)  # 51MB
        large_file = SimpleUploadedFile('large.txt', large_content)
        response = self.client.post(upload_url, {'file': large_file}, format='multipart')
        self.assertEqual(response.status_code, 400)

class MiddlewareTests(TestCase):
    """
    Тестове за custom middleware.
    """

    def test_health_check_endpoint(self):
        """Тества health check endpoint."""
        response = self.client.get('/health/')
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['status'], 'healthy')
        self.assertIn('timestamp', data)
        self.assertEqual(data['service'], 'ai-tutor-platform')

    def test_security_headers(self):
        """Тества добавянето на security headers."""
        response = self.client.get('/')

        # Проверка за security headers
        self.assertIn('Content-Security-Policy', response)
        self.assertIn('X-Content-Type-Options', response)
        self.assertIn('X-Frame-Options', response)
        self.assertIn('X-XSS-Protection', response)
        self.assertIn('Referrer-Policy', response)

    @skipIf(not genanki_installed, "genanki не е инсталиран")
    def test_anki_export_no_flashcards(self):
        """Тества Anki експорт при липса на флашкарти."""
        doc = UploadedDocument.objects.create(file=SimpleUploadedFile('a.txt', b'test'), status='ready')
        url = reverse('document-export-anki', kwargs={'document_id': doc.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    @skipIf(not genanki_installed, "genanki не е инсталиран")
    def test_anki_export_invalid_doc(self):
        """Тества Anki експорт при невалиден document_id."""
        url = reverse('document-export-anki', kwargs={'document_id': '00000000-0000-0000-0000-000000000000'})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_csv_export(self):
        """Тества CSV експорт на въпроси за документ."""
        doc = UploadedDocument.objects.create(file=SimpleUploadedFile('a.txt', b'test'), status='ready')
        Question.objects.create(document=doc, question_text='Q?', choices=['A','B','C','D'], correct_choice='A', explanation='exp')
        url = reverse('document-export-csv', kwargs={'document_id': doc.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['content-type'], 'text/csv; charset=utf-8')
        file_bytes = b''.join(response.streaming_content)
        content = file_bytes.decode('utf-8')
        self.assertIn('Q?', content)
        self.assertIn('A', content)

    def test_csv_export_no_questions(self):
        """Тества CSV експорт при липса на въпроси."""
        doc = UploadedDocument.objects.create(file=SimpleUploadedFile('a.txt', b'test'), status='ready')
        url = reverse('document-export-csv', kwargs={'document_id': doc.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_csv_export_invalid_doc(self):
        """Тества CSV експорт при невалиден document_id."""
        url = reverse('document-export-csv', kwargs={'document_id': '00000000-0000-0000-0000-000000000000'})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)
