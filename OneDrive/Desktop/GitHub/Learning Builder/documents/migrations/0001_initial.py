# Generated by Django 5.2.1 on 2025-06-28 17:21

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Flashcard',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('front', models.TextField(validators=[django.core.validators.MinLengthValidator(2)])),
                ('back', models.TextField(validators=[django.core.validators.MinLengthValidator(2)])),
                ('difficulty', models.CharField(choices=[('easy', 'Лесно'), ('medium', 'Средно'), ('hard', 'Трудно')], default='medium', max_length=10)),
                ('tags', models.JSONField(blank=True, default=list)),
                ('ease_factor', models.FloatField(default=2.5)),
                ('interval_days', models.PositiveIntegerField(default=1)),
                ('next_review_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('times_reviewed', models.PositiveIntegerField(default=0)),
                ('times_correct', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['next_review_date'],
            },
        ),
        migrations.CreateModel(
            name='Quiz',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('time_limit_minutes', models.PositiveIntegerField(blank=True, null=True)),
                ('shuffle_questions', models.BooleanField(default=True)),
                ('show_correct_answers', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('times_taken', models.PositiveIntegerField(default=0)),
                ('average_score', models.FloatField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Course',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, validators=[django.core.validators.MinLengthValidator(2)])),
                ('description', models.TextField(blank=True)),
                ('color', models.CharField(default='#3498db', max_length=7)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='courses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-updated_at'],
                'unique_together': {('owner', 'name')},
            },
        ),
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200, validators=[django.core.validators.MinLengthValidator(2)])),
                ('file', models.FileField(upload_to='documents/%Y/%m/')),
                ('file_size', models.PositiveIntegerField()),
                ('file_type', models.CharField(choices=[('pdf', 'PDF'), ('docx', 'DOCX'), ('txt', 'TXT')], max_length=10)),
                ('status', models.CharField(choices=[('uploaded', 'Качен'), ('processing', 'Обработва се'), ('ready', 'Готов'), ('failed', 'Неуспешен')], default='uploaded', max_length=20)),
                ('extracted_text', models.TextField(blank=True)),
                ('ai_summary', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_accessed', models.DateTimeField(blank=True, null=True)),
                ('view_count', models.PositiveIntegerField(default=0)),
                ('study_sessions_count', models.PositiveIntegerField(default=0)),
                ('course', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='documents.course')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('question_text', models.TextField(validators=[django.core.validators.MinLengthValidator(5)])),
                ('question_type', models.CharField(choices=[('multiple_choice', 'Избираем отговор'), ('true_false', 'Вярно/Невярно'), ('short_answer', 'Кратък отговор'), ('essay', 'Есе')], default='multiple_choice', max_length=20)),
                ('choices', models.JSONField(default=list)),
                ('correct_choice', models.CharField(blank=True, max_length=200)),
                ('correct_answer', models.TextField(blank=True)),
                ('explanation', models.TextField(blank=True)),
                ('points', models.PositiveIntegerField(default=1)),
                ('order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='documents.quiz')),
            ],
            options={
                'ordering': ['order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='StudySession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_type', models.CharField(choices=[('flashcards', 'Флашкарти'), ('quiz', 'Тест'), ('review', 'Преглед')], max_length=20)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('ended_at', models.DateTimeField(blank=True, null=True)),
                ('duration_minutes', models.PositiveIntegerField(blank=True, null=True)),
                ('total_items', models.PositiveIntegerField(default=0)),
                ('correct_answers', models.PositiveIntegerField(default=0)),
                ('score_percentage', models.FloatField(blank=True, null=True)),
                ('completed', models.BooleanField(default=False)),
                ('notes', models.TextField(blank=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='study_sessions', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='QuizAttempt',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('answers', models.JSONField(default=dict)),
                ('score', models.FloatField(blank=True, null=True)),
                ('max_score', models.FloatField(blank=True, null=True)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('submitted_at', models.DateTimeField(blank=True, null=True)),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attempts', to='documents.quiz')),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quiz_attempts', to='documents.studysession')),
            ],
        ),
        migrations.CreateModel(
            name='FlashcardReview',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('response_quality', models.IntegerField(choices=[(0, 'Не знам'), (1, 'Трудно'), (2, 'Добре'), (3, 'Лесно'), (4, 'Перфектно')])),
                ('response_time_seconds', models.PositiveIntegerField()),
                ('reviewed_at', models.DateTimeField(auto_now_add=True)),
                ('flashcard', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='documents.flashcard')),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='flashcard_reviews', to='documents.studysession')),
            ],
        ),
        migrations.CreateModel(
            name='StudySet',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('content_type', models.CharField(choices=[('flashcards', 'Флашкарти'), ('quiz', 'Тест'), ('explanations', 'Обяснения'), ('mixed', 'Смесено')], max_length=20)),
                ('ai_prompt_used', models.TextField(blank=True)),
                ('ai_model_version', models.CharField(default='gpt-3.5-turbo', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('times_studied', models.PositiveIntegerField(default=0)),
                ('average_score', models.FloatField(blank=True, null=True)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='study_sets', to='documents.document')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='studysession',
            name='study_set',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to='documents.studyset'),
        ),
        migrations.AddField(
            model_name='quiz',
            name='study_set',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quizzes', to='documents.studyset'),
        ),
        migrations.AddField(
            model_name='flashcard',
            name='study_set',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='flashcards', to='documents.studyset'),
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('preferred_language', models.CharField(choices=[('bg', 'Български'), ('en', 'English')], default='bg', max_length=10)),
                ('ai_difficulty_level', models.CharField(choices=[('easy', 'Лесно'), ('medium', 'Средно'), ('hard', 'Трудно')], default='medium', max_length=20)),
                ('total_documents_uploaded', models.PositiveIntegerField(default=0)),
                ('total_tests_taken', models.PositiveIntegerField(default=0)),
                ('total_study_time_minutes', models.PositiveIntegerField(default=0)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
