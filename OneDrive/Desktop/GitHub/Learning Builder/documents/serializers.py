"""
Подобрени serializers за новата структура на моделите.
"""
from rest_framework import serializers
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from drf_spectacular.utils import extend_schema_serializer, OpenApiExample
from .models import (
    UserProfile, Course, Document, StudySet, 
    Quiz, Question, Flashcard, StudySession,
    FlashcardReview, QuizAttempt
)

def validate_file_extension(value):
    """
    Валидира, че файлът е с разширение pdf, docx или txt.
    """
    allowed = ('.pdf', '.docx', '.txt')
    if not value.name.lower().endswith(allowed):
        raise ValidationError("Unsupported file type. Позволени са само PDF, DOCX, TXT.")

def validate_file_size(value):
    """
    Валидира размера на файла - максимум 50MB.
    """
    max_size = 50 * 1024 * 1024  # 50MB в байтове
    if value.size > max_size:
        raise ValidationError(f"Файлът е твърде голям. Максимален размер: 50MB. Вашият файл: {value.size / (1024*1024):.1f}MB")

def validate_file_content(value):
    """
    Основна валидация на съдържанието на файла.
    """
    # Проверка за минимален размер
    min_size = 100  # 100 байта минимум
    if value.size < min_size:
        raise ValidationError("Файлът е твърде малък за обработка.")
    
    # Проверка за подозрителни файлове
    dangerous_extensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com']
    file_name_lower = value.name.lower()
    for ext in dangerous_extensions:
        if ext in file_name_lower:
            raise ValidationError("Този тип файл не е разрешен от съображения за сигурност.")
    
    # Проверка за валидно име на файл
    if len(value.name) > 255:
        raise ValidationError("Името на файла е твърде дълго.")
    
    # Проверка за валидни символи в името
    import re
    if not re.match(r'^[a-zA-Z0-9._\-\s\u0400-\u04FF]+$', value.name):
        raise ValidationError("Името на файла съдържа невалидни символи.")

class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer за потребителски профил."""
    username = serializers.CharField(source='user.username', read_only=True)
    email = serializers.EmailField(source='user.email', read_only=True)
    
    class Meta:
        model = UserProfile
        fields = [
            'username', 'email', 'preferred_language', 'ai_difficulty_level',
            'total_documents_uploaded', 'total_tests_taken', 'total_study_time_minutes',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'total_documents_uploaded', 'total_tests_taken', 'total_study_time_minutes',
            'created_at', 'updated_at'
        ]

class CourseSerializer(serializers.ModelSerializer):
    """Serializer за курсове."""
    documents_count = serializers.SerializerMethodField()
    study_sets_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Course
        fields = [
            'id', 'name', 'description', 'color',
            'documents_count', 'study_sets_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_documents_count(self, obj):
        return obj.documents.count()
    
    def get_study_sets_count(self, obj):
        return StudySet.objects.filter(document__course=obj).count()

@extend_schema_serializer(
    examples=[
        OpenApiExample(
            'Valid file upload',
            summary='Example of a valid file upload',
            description='Upload a PDF, DOCX, or TXT file for processing',
            value={
                'file': 'document.pdf',
                'title': 'My Document'
            },
        ),
    ]
)
class DocumentSerializer(serializers.ModelSerializer):
    """Serializer за документи."""
    course_name = serializers.CharField(source='course.name', read_only=True)
    file_size_mb = serializers.SerializerMethodField()
    file = serializers.FileField(validators=[validate_file_extension, validate_file_size, validate_file_content])
    
    class Meta:
        model = Document
        fields = [
            'id', 'title', 'file', 'file_size', 'file_size_mb', 'file_type', 'status',
            'course', 'course_name', 'extracted_text', 'ai_summary',
            'view_count', 'study_sessions_count',
            'created_at', 'updated_at', 'last_accessed'
        ]
        read_only_fields = [
            'id', 'title', 'file_size', 'file_type', 'status', 'extracted_text', 'ai_summary',
            'view_count', 'study_sessions_count', 'created_at', 'updated_at', 'last_accessed'
        ]
    
    def get_file_size_mb(self, obj):
        return round(obj.file_size / (1024 * 1024), 2) if obj.file_size else 0
    
    def create(self, validated_data):
        # Автоматично извличане на заглавие от името на файла ако не е зададено
        if not validated_data.get('title'):
            file_name = validated_data['file'].name
            validated_data['title'] = file_name.rsplit('.', 1)[0]
        
        # Автоматично определяне на file_size и file_type
        file = validated_data['file']
        validated_data['file_size'] = file.size
        
        file_name = file.name.lower()
        if file_name.endswith('.pdf'):
            validated_data['file_type'] = 'pdf'
        elif file_name.endswith('.docx'):
            validated_data['file_type'] = 'docx'
        elif file_name.endswith('.txt'):
            validated_data['file_type'] = 'txt'
        
        # Задаване на owner от request
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            validated_data['owner'] = request.user
        
        return super().create(validated_data)

class FlashcardSerializer(serializers.ModelSerializer):
    """Serializer за флашкарти."""
    success_rate = serializers.ReadOnlyField()
    is_due = serializers.SerializerMethodField()
    
    class Meta:
        model = Flashcard
        fields = [
            'id', 'front', 'back', 'difficulty', 'tags',
            'ease_factor', 'interval_days', 'next_review_date',
            'times_reviewed', 'times_correct', 'success_rate', 'is_due',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'ease_factor', 'interval_days', 'next_review_date',
            'times_reviewed', 'times_correct', 'created_at', 'updated_at'
        ]
    
    def get_is_due(self, obj):
        from django.utils import timezone
        return obj.next_review_date <= timezone.now()

class QuestionSerializer(serializers.ModelSerializer):
    """Serializer за въпроси."""
    
    class Meta:
        model = Question
        fields = [
            'id', 'question_text', 'question_type', 'choices',
            'correct_choice', 'correct_answer', 'explanation',
            'points', 'order', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']

class QuizSerializer(serializers.ModelSerializer):
    """Serializer за тестове."""
    questions = QuestionSerializer(many=True, read_only=True)
    questions_count = serializers.SerializerMethodField()
    total_points = serializers.SerializerMethodField()
    
    class Meta:
        model = Quiz
        fields = [
            'id', 'title', 'description', 'time_limit_minutes',
            'shuffle_questions', 'show_correct_answers',
            'questions', 'questions_count', 'total_points',
            'times_taken', 'average_score',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'times_taken', 'average_score', 'created_at', 'updated_at'
        ]
    
    def get_questions_count(self, obj):
        return obj.questions.count()
    
    def get_total_points(self, obj):
        return sum(q.points for q in obj.questions.all())

class StudySetSerializer(serializers.ModelSerializer):
    """Serializer за study sets."""
    document_title = serializers.CharField(source='document.title', read_only=True)
    items_count = serializers.SerializerMethodField()
    
    class Meta:
        model = StudySet
        fields = [
            'id', 'name', 'description', 'content_type',
            'document', 'document_title', 'items_count',
            'ai_prompt_used', 'ai_model_version',
            'times_studied', 'average_score',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'ai_prompt_used', 'ai_model_version',
            'times_studied', 'average_score', 'created_at', 'updated_at'
        ]
    
    def get_items_count(self, obj):
        if obj.content_type == 'flashcards':
            return obj.flashcards.count()
        elif obj.content_type == 'quiz':
            return sum(quiz.questions.count() for quiz in obj.quizzes.all())
        return 0

class StudySessionSerializer(serializers.ModelSerializer):
    """Serializer за сесии на учене."""
    study_set_name = serializers.CharField(source='study_set.name', read_only=True)
    duration_formatted = serializers.SerializerMethodField()
    
    class Meta:
        model = StudySession
        fields = [
            'id', 'session_type', 'study_set_name',
            'started_at', 'ended_at', 'duration_minutes', 'duration_formatted',
            'total_items', 'correct_answers', 'score_percentage',
            'completed', 'notes'
        ]
        read_only_fields = [
            'id', 'started_at', 'duration_minutes', 'score_percentage'
        ]
    
    def get_duration_formatted(self, obj):
        if obj.duration_minutes:
            hours = obj.duration_minutes // 60
            minutes = obj.duration_minutes % 60
            if hours > 0:
                return f"{hours}ч {minutes}мин"
            return f"{minutes}мин"
        return "N/A"
