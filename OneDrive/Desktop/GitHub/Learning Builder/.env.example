# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
DB_NAME=learning_builder_db
DB_USER=postgres-user
DB_PASSWORD=password
DB_HOST=127.0.0.1
DB_PORT=5432

# Celery & Redis Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# OpenAI API Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Optional: Production settings
# DEBUG=False
# ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
